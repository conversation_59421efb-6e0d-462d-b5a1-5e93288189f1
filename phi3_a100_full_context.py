#!/usr/bin/env python3
"""
Phi-3-Vision A100 Full Context Inference
No truncation, full 128k context, similarity scoring, comprehensive logging
Optimized for A100 80GB GPU with paged attention
"""

import json
import time
import base64
import logging
import difflib
from io import BytesIO
from datetime import datetime
from typing import List, Dict, Any, Tuple
from pathlib import Path

from PIL import Image
from vllm import LLM, SamplingParams
import numpy as np

# Setup comprehensive logging
def setup_logging():
    """Setup detailed logging with file and console output"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = f"phi3_a100_inference_{timestamp}.log"

    # Create logs directory if it doesn't exist
    Path("logs").mkdir(exist_ok=True)
    log_path = Path("logs") / log_file

    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_path, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

    logger = logging.getLogger(__name__)
    logger.info(f"🗂️ Logging to: {log_path}")
    return logger, log_path

def remove_urls_from_text(text: str) -> Tuple[str, int]:
    """Remove all URLs from text to reduce token count"""
    import re

    url_patterns = [
        r'https?://[^\s<>"]+',
        r'www\.[^\s<>"]+',
        r'ftp://[^\s<>"]+',
        r'[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(?:/[^\s<>"]*)?',
    ]

    cleaned_text = text
    urls_removed = 0

    for pattern in url_patterns:
        matches = re.findall(pattern, cleaned_text)
        urls_removed += len(matches)
        cleaned_text = re.sub(pattern, '', cleaned_text)

    # Clean up whitespace
    cleaned_text = re.sub(r'\s+', ' ', cleaned_text)
    cleaned_text = re.sub(r'\n\s*\n\s*\n', '\n\n', cleaned_text)
    cleaned_text = cleaned_text.strip()

    return cleaned_text, urls_removed

def calculate_similarity(text1: str, text2: str) -> Dict[str, float]:
    """Calculate multiple similarity metrics between two texts"""

    # Basic similarity using difflib
    sequence_similarity = difflib.SequenceMatcher(None, text1, text2).ratio()

    # Word-level similarity
    words1 = set(text1.lower().split())
    words2 = set(text2.lower().split())

    if len(words1) == 0 and len(words2) == 0:
        word_similarity = 1.0
    elif len(words1) == 0 or len(words2) == 0:
        word_similarity = 0.0
    else:
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        word_similarity = intersection / union if union > 0 else 0.0

    # Length similarity
    len1, len2 = len(text1), len(text2)
    length_similarity = 1.0 - abs(len1 - len2) / max(len1, len2, 1)

    # Character-level similarity
    char_similarity = sum(1 for a, b in zip(text1, text2) if a == b) / max(len1, len2, 1)

    return {
        'sequence_similarity': sequence_similarity,
        'word_similarity': word_similarity,
        'length_similarity': length_similarity,
        'character_similarity': char_similarity,
        'average_similarity': (sequence_similarity + word_similarity + length_similarity + char_similarity) / 4
    }

def load_prompts_from_jsonl(file_path: str, logger: logging.Logger) -> List[Dict]:
    """Load prompts from JSONL file with comprehensive logging"""
    logger.info(f"📂 Loading prompts from {file_path}...")

    try:
        prompts = []
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Handle formatted JSON records
        brace_count = 0
        current_record = ""

        for line in content.split('\n'):
            if line.strip() == '{' and brace_count == 0:
                if current_record.strip():
                    try:
                        record = json.loads(current_record.strip())
                        prompts.append(record)
                    except json.JSONDecodeError:
                        pass
                current_record = line + '\n'
                brace_count = 1
            elif brace_count > 0:
                current_record += line + '\n'
                brace_count += line.count('{') - line.count('}')

                if brace_count == 0:
                    try:
                        record = json.loads(current_record.strip())
                        prompts.append(record)
                    except json.JSONDecodeError:
                        pass
                    current_record = ""

        if current_record.strip():
            try:
                record = json.loads(current_record.strip())
                prompts.append(record)
            except json.JSONDecodeError:
                pass

        logger.info(f"✅ Loaded {len(prompts)} prompts successfully")
        return prompts

    except Exception as e:
        logger.error(f"❌ Error loading prompts: {str(e)}")
        return []

def prepare_image_for_a100(base64_data: str, logger: logging.Logger) -> Image.Image:
    """Prepare image for A100 processing - minimal resizing to preserve quality"""
    try:
        if base64_data.startswith('data:'):
            header, base64_content = base64_data.split(',', 1)
        else:
            base64_content = base64_data.strip()

        image_bytes = base64.b64decode(base64_content)
        image = Image.open(BytesIO(image_bytes))

        if image.mode != 'RGB':
            image = image.convert('RGB')

        # Minimal resizing for A100 - preserve quality
        max_size = 1024  # Higher resolution for A100
        if max(image.size) > max_size:
            original_size = image.size
            image.thumbnail((max_size, max_size), Image.Resampling.LANCZOS)
            logger.info(f"    📏 Resized: {original_size} → {image.size}")

        return image
    except Exception as e:
        logger.error(f"    ❌ Image error: {e}")
        return None

def initialize_phi3_a100(logger: logging.Logger) -> Tuple[LLM, int]:
    """Initialize Phi-3-Vision optimized for A100 80GB with full context"""

    # A100 80GB can handle much larger contexts
    context_attempts = [
        {"context": 128000, "gpu_util": 0.9, "description": "Full 128k context"},
        {"context": 120000, "gpu_util": 0.85, "description": "120k context"},
        {"context": 100000, "gpu_util": 0.8, "description": "100k context"},
        {"context": 80000, "gpu_util": 0.75, "description": "80k context"},
        {"context": 60000, "gpu_util": 0.7, "description": "60k context"}
    ]

    logger.info("🚀 Initializing Phi-3-Vision for A100 80GB GPU")
    logger.info("🎯 Target: Full 128k context without truncation")

    for attempt in context_attempts:
        try:
            logger.info(f"🔄 Trying {attempt['description']}...")

            # Calculate estimated memory usage
            estimated_memory = (attempt["context"] / 1000) * 0.6  # A100 is more efficient
            logger.info(f"    📊 Estimated memory: ~{estimated_memory:.1f} GiB")

            llm = LLM(
                model="microsoft/Phi-3-vision-128k-instruct",
                trust_remote_code=True,
                max_model_len=attempt["context"],
                gpu_memory_utilization=attempt["gpu_util"],
                swap_space=16,  # A100 can handle more swap
                max_num_seqs=1,
                limit_mm_per_prompt={"image": 5},  # A100 can handle more images
                enforce_eager=False,  # Use torch.compile for A100 optimization
                # Note: Paged attention is enabled by default in modern vLLM
            )

            logger.info(f"✅ SUCCESS: {attempt['description']}")
            logger.info(f"    💾 GPU utilization: {attempt['gpu_util']*100:.0f}%")
            logger.info(f"    🧠 Paged attention: ENABLED")
            logger.info(f"    ⚡ Torch compile: ENABLED")

            return llm, attempt["context"]

        except Exception as e:
            error_msg = str(e)
            logger.error(f"❌ Failed: {error_msg[:200]}...")

            if "memory" in error_msg.lower():
                logger.warning("    💡 Memory issue - trying smaller context")

            continue

    raise Exception("Could not initialize Phi-3-Vision with any context size")

def estimate_tokens_a100(text: str, num_images: int, logger: logging.Logger) -> Dict[str, int]:
    """Estimate token usage for A100 processing"""

    # More accurate estimates for A100
    chars_per_token = 3.2  # Phi-3-Vision tokenization efficiency
    tokens_per_image = 800  # Conservative estimate for high-res images

    text_tokens = len(text) / chars_per_token
    image_tokens = num_images * tokens_per_image
    total_tokens = text_tokens + image_tokens

    logger.info(f"📊 Token estimation:")
    logger.info(f"    Text: {text_tokens:,.0f} tokens ({len(text):,} chars)")
    logger.info(f"    Images: {image_tokens:,.0f} tokens ({num_images} images)")
    logger.info(f"    Total: {total_tokens:,.0f} tokens")

    return {
        'text_tokens': int(text_tokens),
        'image_tokens': int(image_tokens),
        'total_tokens': int(total_tokens)
    }

def main():
    """Main A100 full context inference with similarity scoring"""

    # Setup logging
    logger, log_file = setup_logging()

    logger.info("🌟 Phi-3-Vision A100 Full Context Inference")
    logger.info("=" * 60)
    logger.info("🎯 Features: No truncation, Full 128k context, Similarity scoring")
    logger.info("🖥️ Optimized for: A100 80GB GPU with paged attention")

    # Load prompts
    input_files = [
        'final_image_prompts_cleaned.jsonl',
        'scrape_content_prompts.jsonl'
    ]

    prompts = None
    for input_file in input_files:
        prompts = load_prompts_from_jsonl(input_file, logger)
        if prompts:
            logger.info(f"✅ Using prompts from: {input_file}")
            break

    if not prompts:
        logger.error("❌ No prompts found")
        return

    # Initialize model
    try:
        llm, max_context = initialize_phi3_a100(logger)
    except Exception as e:
        logger.error(f"❌ Model initialization failed: {e}")
        return

    # Process prompts
    results = []
    max_prompts = min(len(prompts), 10)  # Process up to 10 prompts

    logger.info(f"\n🚀 PROCESSING {max_prompts} PROMPTS")
    logger.info("=" * 60)

    for i, prompt_data in enumerate(prompts[:max_prompts], 1):
        logger.info(f"\n{'='*20} PROMPT {i}/{max_prompts} {'='*20}")

        try:
            # Extract data
            system_prompt = prompt_data.get('SystemPrompt', '')
            user_prompt = prompt_data.get('UserPrompt', '')
            image_data_urls = prompt_data.get('Images', [])
            actual_response = prompt_data.get('Response', '')

            logger.info(f"📋 Original prompt lengths:")
            logger.info(f"    System: {len(system_prompt):,} chars")
            logger.info(f"    User: {len(user_prompt):,} chars")
            logger.info(f"    Images: {len(image_data_urls)}")
            logger.info(f"    Actual response: {len(actual_response):,} chars")

            # Remove URLs to maximize context efficiency
            logger.info("🔗 Removing URLs to maximize context efficiency...")
            cleaned_system, system_urls = remove_urls_from_text(system_prompt)
            cleaned_user, user_urls = remove_urls_from_text(user_prompt)

            total_urls = system_urls + user_urls
            total_saved = (len(system_prompt) - len(cleaned_system)) + (len(user_prompt) - len(cleaned_user))

            logger.info(f"📝 URL removal results:")
            logger.info(f"    URLs removed: {total_urls}")
            logger.info(f"    Characters saved: {total_saved:,}")
            logger.info(f"    System: {len(system_prompt):,} → {len(cleaned_system):,} chars")
            logger.info(f"    User: {len(user_prompt):,} → {len(cleaned_user):,} chars")

            # Process images with high quality for A100
            processed_images = []
            logger.info(f"🖼️ Processing {len(image_data_urls)} images for A100...")

            for j, img_data in enumerate(image_data_urls):
                image = prepare_image_for_a100(img_data, logger)
                if image:
                    processed_images.append(image)
                    logger.info(f"    ✓ Image {j+1}: {image.size}")
                else:
                    logger.warning(f"    ✗ Image {j+1}: failed")

            if not processed_images:
                logger.error("❌ No valid images, skipping prompt")
                continue

            # Estimate tokens (no truncation needed with A100)
            estimate_tokens_a100(cleaned_system + cleaned_user, len(processed_images), logger)

            # Create Phi-3-Vision prompt (NO TRUNCATION)
            image_tokens = "".join([f"<|image_{i+1}|>" for i in range(len(processed_images))])
            full_prompt = f"<|user|>\n{image_tokens}\n{cleaned_system}\n\n{cleaned_user}<|end|>\n<|assistant|>\n"

            logger.info(f"📏 Final prompt length: {len(full_prompt):,} chars (NO TRUNCATION)")

            # Sampling parameters for high-quality responses
            sampling_params = SamplingParams(
                temperature=0.1,
                top_p=0.9,
                max_tokens=4096,  # Allow long responses
                repetition_penalty=1.05,
                stop=["<|end|>", "<|user|>", "<|assistant|>"]
            )

            # Run inference
            logger.info("🚀 Running full context inference...")
            start_time = time.time()

            outputs = llm.generate(
                [{
                    "prompt": full_prompt,
                    "multi_modal_data": {"image": processed_images}
                }],
                sampling_params=sampling_params
            )

            inference_time = time.time() - start_time

            if outputs and outputs[0].outputs:
                generated_text = outputs[0].outputs[0].text.strip()

                logger.info(f"✅ Inference completed in {inference_time:.2f}s")
                logger.info(f"📝 Generated response: {len(generated_text):,} chars")

                # Calculate similarity with actual response
                if actual_response:
                    similarity_metrics = calculate_similarity(generated_text, actual_response)

                    logger.info("🔍 SIMILARITY ANALYSIS:")
                    logger.info(f"    Sequence similarity: {similarity_metrics['sequence_similarity']:.3f}")
                    logger.info(f"    Word similarity: {similarity_metrics['word_similarity']:.3f}")
                    logger.info(f"    Length similarity: {similarity_metrics['length_similarity']:.3f}")
                    logger.info(f"    Character similarity: {similarity_metrics['character_similarity']:.3f}")
                    logger.info(f"    📊 AVERAGE SIMILARITY: {similarity_metrics['average_similarity']:.3f}")
                else:
                    similarity_metrics = None
                    logger.warning("⚠️ No actual response available for comparison")

                # Store results
                result = {
                    'prompt_id': f"prompt_{i}",
                    'original_system_length': len(system_prompt),
                    'original_user_length': len(user_prompt),
                    'cleaned_system_length': len(cleaned_system),
                    'cleaned_user_length': len(cleaned_user),
                    'urls_removed': total_urls,
                    'chars_saved': total_saved,
                    'num_images': len(processed_images),
                    'max_context_used': max_context,
                    'no_truncation': True,
                    'actual_response': actual_response,
                    'generated_response': generated_text,
                    'actual_length': len(actual_response),
                    'generated_length': len(generated_text),
                    'inference_time': inference_time,
                    'similarity_metrics': similarity_metrics,
                    'success': True
                }

                results.append(result)

                logger.info(f"📄 Response preview: {generated_text[:200]}...")

            else:
                logger.error("❌ No output generated")

        except Exception as e:
            logger.error(f"❌ Error processing prompt {i}: {e}")
            results.append({
                'prompt_id': f"prompt_{i}",
                'success': False,
                'error': str(e)
            })

    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"phi3_a100_results_{timestamp}.json"

    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)

    # Final summary
    successful = [r for r in results if r.get('success', False)]

    logger.info(f"\n📊 FINAL SUMMARY:")
    logger.info("=" * 60)
    logger.info(f"🖥️ GPU: A100 80GB")
    logger.info(f"🧠 Context: {max_context:,} tokens (NO TRUNCATION)")
    logger.info(f"📊 Processed: {len(results)} prompts")
    logger.info(f"✅ Successful: {len(successful)}")

    if successful:
        avg_similarity = sum(r['similarity_metrics']['average_similarity']
                           for r in successful if r.get('similarity_metrics')) / len(successful)
        avg_time = sum(r['inference_time'] for r in successful) / len(successful)
        total_urls = sum(r['urls_removed'] for r in successful)
        total_saved = sum(r['chars_saved'] for r in successful)

        logger.info(f"🔍 Average similarity: {avg_similarity:.3f}")
        logger.info(f"⏱️ Average inference time: {avg_time:.2f}s")
        logger.info(f"🔗 Total URLs removed: {total_urls:,}")
        logger.info(f"💾 Total characters saved: {total_saved:,}")

    logger.info(f"\n💾 Results saved to: {results_file}")
    logger.info(f"📋 Logs saved to: {log_file}")
    logger.info("🎉 A100 full context inference completed!")

if __name__ == "__main__":
    main()
