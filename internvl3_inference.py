#!/usr/bin/env python3
"""
InternVL3-8B Vision-Language Model Inference
Clean architecture with utils, URL removal, temperature control, memory optimization
"""

import json
import time
import torch
import gc
import os
from datetime import datetime

# Set memory optimization environment variables
os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "expandable_segments:True"

# Import utilities from utils folder
from utils.data_loader import load_prompts_from_jsonl
from utils.text_processor import remove_urls_from_text, estimate_tokens
from utils.internvl_model_initializer import initialize_internvl3, create_generation_config
from utils.internvl_image_processor import (
    prepare_image_from_base64,
    combine_images,
    create_image_prompt
)

def main():
    """Clean InternVL3-8B inference with URL removal"""
    print("🌟 InternVL3-8B Vision-Language Model Inference")
    print("=" * 60)
    print("🎯 Features: URL removal, No truncation, Temperature 0.3")

    # Load prompts
    input_files = ['final_image_prompts_cleaned.jsonl', 'scrape_content_prompts.jsonl']
    prompts = None

    for input_file in input_files:
        prompts = load_prompts_from_jsonl(input_file)
        if prompts:
            print(f"✅ Using prompts from: {input_file}")
            break

    if not prompts:
        print("❌ No prompts found")
        return

    # Initialize InternVL3-8B model
    print(f"\n🤖 INITIALIZING INTERNVL3-8B MODEL")
    print("-" * 50)

    try:
        model, tokenizer = initialize_internvl3()
        print(f"✅ InternVL3-8B ready for inference")
    except Exception as e:
        print(f"❌ Model initialization failed: {e}")
        return

    # Create generation config
    generation_config = create_generation_config(max_tokens=2000, temperature=0.3)
    print(f"🎛️ Generation config: {generation_config}")

    # Process prompts
    results = []
    max_prompts = min(len(prompts), 5)  # Process up to 5 prompts

    print(f"\n🚀 PROCESSING {max_prompts} PROMPTS")
    print("=" * 60)

    for i, prompt_data in enumerate(prompts[:max_prompts], 1):
        print(f"\n{'='*15} PROMPT {i}/{max_prompts} {'='*15}")

        try:
            # Extract data
            system_prompt = prompt_data.get('SystemPrompt', '')
            user_prompt = prompt_data.get('UserPrompt', '')
            image_data_urls = prompt_data.get('Images', [])

            print(f"📋 Original lengths:")
            print(f"  System: {len(system_prompt):,} chars")
            print(f"  User: {len(user_prompt):,} chars")
            print(f"  Images: {len(image_data_urls)}")

            # Remove URLs to reduce token count
            print("🔗 Removing URLs...")
            cleaned_system, system_urls = remove_urls_from_text(system_prompt)
            cleaned_user, user_urls = remove_urls_from_text(user_prompt)

            total_urls = system_urls + user_urls
            total_saved = (len(system_prompt) - len(cleaned_system)) + (len(user_prompt) - len(cleaned_user))

            print(f"📝 URL removal results:")
            print(f"  URLs removed: {total_urls}")
            print(f"  Characters saved: {total_saved:,}")
            print(f"  Final system: {len(cleaned_system):,} chars")
            print(f"  Final user: {len(cleaned_user):,} chars")

            # Clear GPU cache before processing images
            torch.cuda.empty_cache()
            gc.collect()

            # Process images for InternVL3 with shape safety
            processed_images = []
            print(f"🖼️ Processing images for InternVL3 (shape-safe)...")

            # Process only ONE image to avoid shape conflicts
            if not image_data_urls:
                print("❌ No images, skipping prompt")
                continue

            # Try processing first image with shape validation
            print(f"🖼️ Processing first image only (single tile)...")
            pixel_values = prepare_image_from_base64(
                image_data_urls[0],
                input_size=448,  # Standard size
                max_num=1  # Single tile only
            )

            if pixel_values is not None:
                processed_images.append(pixel_values)
                print(f"  ✓ Image 1: {pixel_values.shape}")
            else:
                # Try safe fallback processing
                print(f"  🔄 Trying safe fallback processing...")
                from utils.internvl_image_processor import prepare_image_safe_fallback
                pixel_values = prepare_image_safe_fallback(image_data_urls[0])

                if pixel_values is not None:
                    processed_images.append(pixel_values)
                    print(f"  ✓ Image 1 (fallback): {pixel_values.shape}")
                else:
                    print(f"  ✗ Image 1: both methods failed")

            if not processed_images:
                print("❌ No valid images, skipping prompt")
                continue

            # Use single image directly (no combining needed)
            pixel_values = processed_images[0]  # Single image tensor

            # Validate tensor shape before GPU transfer
            print(f"📊 Image tensor shape: {pixel_values.shape}")

            # Expected shapes for InternVL3
            if len(pixel_values.shape) != 4:
                print(f"❌ Invalid tensor dimensions: {len(pixel_values.shape)}, expected 4")
                continue

            batch_size, channels, height, width = pixel_values.shape
            if channels != 3:
                print(f"❌ Invalid channels: {channels}, expected 3")
                continue

            print(f"✅ Tensor shape validation passed: {pixel_values.shape}")

            # Move to GPU with memory monitoring
            device = next(model.parameters()).device

            # Check available memory before moving to GPU
            if torch.cuda.is_available():
                memory_allocated = torch.cuda.memory_allocated(device) / 1024**3
                memory_reserved = torch.cuda.memory_reserved(device) / 1024**3
                print(f"💾 GPU memory before inference: {memory_allocated:.1f}GB allocated, {memory_reserved:.1f}GB reserved")

            try:
                pixel_values = pixel_values.to(torch.bfloat16).to(device)
                print(f"✅ Successfully moved to GPU: {pixel_values.shape}")
            except Exception as gpu_error:
                print(f"❌ GPU transfer failed: {gpu_error}")
                continue

            # Estimate tokens (for information)
            combined_text = cleaned_system + cleaned_user
            token_estimate = estimate_tokens(combined_text, len(processed_images))

            print(f"📊 Token estimation (approximate):")
            print(f"  Text: {token_estimate['text_tokens']:,} tokens")
            print(f"  Images: {token_estimate['image_tokens']:,} tokens")
            print(f"  Total: {token_estimate['total_tokens']:,} tokens")

            # Create simple prompt for single image
            image_prompt = "<image>\n"
            question = f"{cleaned_system}\n\n{cleaned_user}"
            full_question = image_prompt + question

            print(f"📝 Created single image prompt")
            print(f"📝 Question length: {len(full_question):,} chars")

            # Run inference with shape-safe approach
            print("🚀 Running InternVL3 inference (shape-safe)...")
            start_time = time.time()

            try:
                # Use torch.no_grad() to save memory and avoid shape issues
                with torch.no_grad():
                    # Single image inference only
                    response = model.chat(
                        tokenizer,
                        pixel_values,
                        full_question,
                        generation_config
                    )

                inference_time = time.time() - start_time

                # Clear GPU cache after inference
                del pixel_values
                torch.cuda.empty_cache()
                gc.collect()

            except RuntimeError as e:
                if "shape" in str(e) or "size" in str(e):
                    print(f"❌ Shape error during inference: {e}")
                    print("🔄 Trying with safe fallback processing...")

                    # Clear memory and try safe fallback
                    torch.cuda.empty_cache()
                    gc.collect()

                    try:
                        # Use ultra-safe processing
                        from utils.internvl_image_processor import prepare_image_safe_fallback
                        safe_pixel_values = prepare_image_safe_fallback(image_data_urls[0])

                        if safe_pixel_values is not None:
                            safe_pixel_values = safe_pixel_values.to(torch.bfloat16).to(device)

                            with torch.no_grad():
                                response = model.chat(
                                    tokenizer,
                                    safe_pixel_values,
                                    "<image>\nDescribe this image briefly.",
                                    {'max_new_tokens': 500, 'temperature': 0.1, 'do_sample': False}
                                )

                            inference_time = time.time() - start_time
                            print("✅ Safe fallback inference successful")

                            del safe_pixel_values
                            torch.cuda.empty_cache()
                            gc.collect()
                        else:
                            raise e

                    except Exception as fallback_error:
                        print(f"❌ Safe fallback also failed: {fallback_error}")
                        raise e
                else:
                    raise e

            except torch.cuda.OutOfMemoryError as e:
                print(f"❌ CUDA OOM during inference: {e}")
                torch.cuda.empty_cache()
                gc.collect()
                raise e

            print(f"✅ Inference completed in {inference_time:.2f}s")
            print(f"📝 Generated: {len(response):,} chars")
            print(f"📄 Preview: {response[:300]}...")

            # Store results
            result = {
                'prompt_id': f"prompt_{i}",
                'model': "OpenGVLab/InternVL3-8B",
                'original_system_length': len(system_prompt),
                'original_user_length': len(user_prompt),
                'cleaned_system_length': len(cleaned_system),
                'cleaned_user_length': len(cleaned_user),
                'urls_removed': total_urls,
                'chars_saved': total_saved,
                'num_images': len(processed_images),
                'num_patches_list': num_patches_list,
                'total_patches': sum(num_patches_list),
                'generated_response': response,
                'generated_length': len(response),
                'inference_time': inference_time,
                'temperature': generation_config['temperature'],
                'max_new_tokens': generation_config['max_new_tokens'],
                'success': True
            }

            results.append(result)

        except Exception as e:
            print(f"❌ Error processing prompt {i}: {e}")

            # Clean up memory on error
            torch.cuda.empty_cache()
            gc.collect()

            results.append({
                'prompt_id': f"prompt_{i}",
                'success': False,
                'error': str(e)
            })

        # Memory cleanup between prompts
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            gc.collect()
            memory_allocated = torch.cuda.memory_allocated() / 1024**3
            print(f"💾 GPU memory after prompt {i}: {memory_allocated:.1f}GB")

    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f'internvl3_results_{timestamp}.json'

    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)

    # Summary
    successful = [r for r in results if r.get('success', False)]

    print(f"\n📊 SUMMARY:")
    print("=" * 60)
    print(f"🤖 Model: InternVL3-8B")
    print(f"📊 Processed: {len(results)} prompts")
    print(f"✅ Successful: {len(successful)}")
    print(f"🌡️ Temperature: 0.3")

    if successful:
        avg_time = sum(r['inference_time'] for r in successful) / len(successful)
        total_urls = sum(r['urls_removed'] for r in successful)
        total_saved = sum(r['chars_saved'] for r in successful)
        avg_length = sum(r['generated_length'] for r in successful) / len(successful)
        avg_patches = sum(r['total_patches'] for r in successful) / len(successful)

        print(f"⏱️ Average time: {avg_time:.2f}s")
        print(f"🔗 Total URLs removed: {total_urls:,}")
        print(f"💾 Total chars saved: {total_saved:,}")
        print(f"📝 Average response: {avg_length:,.0f} chars")
        print(f"🖼️ Average patches: {avg_patches:.1f}")

    print(f"\n💾 Results saved to: {results_file}")
    print("🎉 InternVL3-8B inference completed!")

if __name__ == "__main__":
    main()
