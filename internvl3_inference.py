#!/usr/bin/env python3
"""
InternVL3-8B Vision-Language Model Inference
Clean architecture with utils, URL removal, temperature control
"""

import json
import time
import torch
from datetime import datetime

# Import utilities from utils folder
from utils.data_loader import load_prompts_from_jsonl
from utils.text_processor import remove_urls_from_text, estimate_tokens
from utils.internvl_model_initializer import initialize_internvl3, create_generation_config
from utils.internvl_image_processor import (
    prepare_image_from_base64,
    combine_images,
    create_image_prompt
)

def main():
    """Clean InternVL3-8B inference with safe precision settings"""
    print("🌟 InternVL3-8B Vision-Language Model Inference")
    print("=" * 60)
    print("🎯 Features: URL removal, No truncation, Safe precision")

    # Configuration - using safer settings to avoid CUDA errors
    model_precision = "8bit"  # Start with 8-bit (more stable than 4-bit)

    print(f"🔢 Model precision: {model_precision} (safer than 4-bit)")
    print(f"🔢 Image dtype will match model dtype automatically")

    # Set CUDA environment for debugging
    import os
    os.environ["CUDA_LAUNCH_BLOCKING"] = "1"

    # Load prompts
    input_files = ['final_image_prompts_cleaned.jsonl', 'scrape_content_prompts.jsonl']
    prompts = None

    for input_file in input_files:
        prompts = load_prompts_from_jsonl(input_file)
        if prompts:
            print(f"✅ Using prompts from: {input_file}")
            break

    if not prompts:
        print("❌ No prompts found")
        return

    # Initialize InternVL3-8B model with specified precision
    print(f"\n🤖 INITIALIZING INTERNVL3-8B MODEL")
    print("-" * 50)

    try:
        model, tokenizer = initialize_internvl3(precision=model_precision)

        # Get model dtype for consistent tensor processing
        model_dtype = next(model.parameters()).dtype
        print(f"✅ InternVL3-8B ready for inference")
        print(f"🔢 Model dtype: {model_dtype}")

    except Exception as e:
        print(f"❌ Model initialization failed: {e}")
        print("💡 Try different precision: 8bit or bfloat16")
        return

    # Create generation config
    generation_config = create_generation_config(max_tokens=2000, temperature=0.3)
    print(f"🎛️ Generation config: {generation_config}")

    # Process prompts
    results = []
    max_prompts = min(len(prompts), 5)  # Process up to 5 prompts

    print(f"\n🚀 PROCESSING {max_prompts} PROMPTS")
    print("=" * 60)

    for i, prompt_data in enumerate(prompts[:max_prompts], 1):
        print(f"\n{'='*15} PROMPT {i}/{max_prompts} {'='*15}")

        try:
            # Extract data
            system_prompt = prompt_data.get('SystemPrompt', '')
            user_prompt = prompt_data.get('UserPrompt', '')
            image_data_urls = prompt_data.get('Images', [])

            print(f"📋 Original lengths:")
            print(f"  System: {len(system_prompt):,} chars")
            print(f"  User: {len(user_prompt):,} chars")
            print(f"  Images: {len(image_data_urls)}")

            # Remove URLs to reduce token count
            print("🔗 Removing URLs...")
            cleaned_system, system_urls = remove_urls_from_text(system_prompt)
            cleaned_user, user_urls = remove_urls_from_text(user_prompt)

            total_urls = system_urls + user_urls
            total_saved = (len(system_prompt) - len(cleaned_system)) + (len(user_prompt) - len(cleaned_user))

            print(f"📝 URL removal results:")
            print(f"  URLs removed: {total_urls}")
            print(f"  Characters saved: {total_saved:,}")
            print(f"  Final system: {len(cleaned_system):,} chars")
            print(f"  Final user: {len(cleaned_user):,} chars")

            # Process images for InternVL3
            processed_images = []
            print(f"🖼️ Processing images for InternVL3...")

            for j, img_data in enumerate(image_data_urls[:3]):  # Limit to 3 images
                pixel_values = prepare_image_from_base64(
                    img_data,
                    input_size=448,
                    max_num=6,
                    precision=image_precision
                )
                if pixel_values is not None:
                    processed_images.append(pixel_values)
                    print(f"  ✓ Image {j+1}: {pixel_values.shape}")
                else:
                    print(f"  ✗ Image {j+1}: failed")

            if not processed_images:
                print("❌ No valid images, skipping prompt")
                continue

            # Combine images and prepare for inference
            combined_pixel_values, num_patches_list = combine_images(processed_images)

            # Move to GPU with appropriate precision and error handling
            device = next(model.parameters()).device

            try:
                # Clear GPU cache before tensor operations
                torch.cuda.empty_cache()

                if image_precision == "float16":
                    combined_pixel_values = combined_pixel_values.to(torch.float16).to(device)
                elif image_precision == "bfloat16":
                    combined_pixel_values = combined_pixel_values.to(torch.bfloat16).to(device)
                else:
                    combined_pixel_values = combined_pixel_values.to(device)

                # Synchronize to catch any CUDA errors early
                torch.cuda.synchronize()

            except Exception as e:
                print(f"❌ Error moving tensors to GPU: {e}")
                print("💡 Try reducing image size or using CPU")
                continue

            print(f"📊 Combined images: {combined_pixel_values.shape}")
            print(f"📊 Patches per image: {num_patches_list}")

            # Estimate tokens (for information)
            combined_text = cleaned_system + cleaned_user
            token_estimate = estimate_tokens(combined_text, len(processed_images))

            print(f"📊 Token estimation (approximate):")
            print(f"  Text: {token_estimate['text_tokens']:,} tokens")
            print(f"  Images: {token_estimate['image_tokens']:,} tokens")
            print(f"  Total: {token_estimate['total_tokens']:,} tokens")

            # Create prompt for InternVL3
            if len(processed_images) == 1:
                # Single image
                image_prompt = "<image>\n"
                question = f"{cleaned_system}\n\n{cleaned_user}"
                full_question = image_prompt + question
            else:
                # Multiple images - use separate format
                image_prompt = create_image_prompt(len(processed_images), mode="separate")
                question = f"{cleaned_system}\n\n{cleaned_user}"
                full_question = image_prompt + question

            print(f"📝 Created InternVL3 prompt format")
            print(f"📝 Question length: {len(full_question):,} chars")
            print(f"🖼️ Images: {len(processed_images)}")

            # Run inference with InternVL3
            print("🚀 Running InternVL3 inference...")
            start_time = time.time()

            if len(processed_images) == 1:
                # Single image inference
                response = model.chat(
                    tokenizer,
                    combined_pixel_values,
                    full_question,
                    generation_config
                )
            else:
                # Multi-image inference
                response = model.chat(
                    tokenizer,
                    combined_pixel_values,
                    full_question,
                    generation_config,
                    num_patches_list=num_patches_list
                )

            inference_time = time.time() - start_time

            print(f"✅ Inference completed in {inference_time:.2f}s")
            print(f"📝 Generated: {len(response):,} chars")
            print(f"📄 Preview: {response[:300]}...")

            # Store results
            result = {
                'prompt_id': f"prompt_{i}",
                'model': "OpenGVLab/InternVL3-8B",
                'original_system_length': len(system_prompt),
                'original_user_length': len(user_prompt),
                'cleaned_system_length': len(cleaned_system),
                'cleaned_user_length': len(cleaned_user),
                'urls_removed': total_urls,
                'chars_saved': total_saved,
                'num_images': len(processed_images),
                'num_patches_list': num_patches_list,
                'total_patches': sum(num_patches_list),
                'generated_response': response,
                'generated_length': len(response),
                'inference_time': inference_time,
                'temperature': generation_config['temperature'],
                'max_new_tokens': generation_config['max_new_tokens'],
                'success': True
            }

            results.append(result)

        except Exception as e:
            print(f"❌ Error processing prompt {i}: {e}")
            results.append({
                'prompt_id': f"prompt_{i}",
                'success': False,
                'error': str(e)
            })

    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f'internvl3_results_{timestamp}.json'

    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)

    # Summary
    successful = [r for r in results if r.get('success', False)]

    print(f"\n📊 SUMMARY:")
    print("=" * 60)
    print(f"🤖 Model: InternVL3-8B")
    print(f"📊 Processed: {len(results)} prompts")
    print(f"✅ Successful: {len(successful)}")
    print(f"🌡️ Temperature: 0.3")

    if successful:
        avg_time = sum(r['inference_time'] for r in successful) / len(successful)
        total_urls = sum(r['urls_removed'] for r in successful)
        total_saved = sum(r['chars_saved'] for r in successful)
        avg_length = sum(r['generated_length'] for r in successful) / len(successful)
        avg_patches = sum(r['total_patches'] for r in successful) / len(successful)

        print(f"⏱️ Average time: {avg_time:.2f}s")
        print(f"🔗 Total URLs removed: {total_urls:,}")
        print(f"💾 Total chars saved: {total_saved:,}")
        print(f"📝 Average response: {avg_length:,.0f} chars")
        print(f"🖼️ Average patches: {avg_patches:.1f}")

    print(f"\n💾 Results saved to: {results_file}")
    print("🎉 InternVL3-8B inference completed!")

if __name__ == "__main__":
    main()
