#!/usr/bin/env python3
"""
Test InternVL3-8B using official example code structure
Optimized for A40 GPU with 8-bit precision
"""

import torch
import torchvision.transforms as T
from PIL import Image
from torchvision.transforms.functional import InterpolationMode
from transformers import AutoModel, AutoTokenizer
import requests
from io import BytesIO

# Constants from official example
IMAGENET_MEAN = (0.485, 0.456, 0.406)
IMAGENET_STD = (0.229, 0.224, 0.225)

def build_transform(input_size):
    """Build image transformation pipeline"""
    MEAN, STD = IMAGENET_MEAN, IMAGENET_STD
    transform = T.Compose([
        T.Lambda(lambda img: img.convert('RGB') if img.mode != 'RGB' else img),
        T.Resize((input_size, input_size), interpolation=InterpolationMode.BICUBIC),
        T.ToTensor(),
        T.Normalize(mean=MEAN, std=STD)
    ])
    return transform

def find_closest_aspect_ratio(aspect_ratio, target_ratios, width, height, image_size):
    """Find the closest aspect ratio from target ratios"""
    best_ratio_diff = float('inf')
    best_ratio = (1, 1)
    area = width * height
    for ratio in target_ratios:
        target_aspect_ratio = ratio[0] / ratio[1]
        ratio_diff = abs(aspect_ratio - target_aspect_ratio)
        if ratio_diff < best_ratio_diff:
            best_ratio_diff = ratio_diff
            best_ratio = ratio
        elif ratio_diff == best_ratio_diff:
            if area > 0.5 * image_size * image_size * ratio[0] * ratio[1]:
                best_ratio = ratio
    return best_ratio

def dynamic_preprocess(image, min_num=1, max_num=12, image_size=448, use_thumbnail=False):
    """Dynamic preprocessing for InternVL3"""
    orig_width, orig_height = image.size
    aspect_ratio = orig_width / orig_height

    # Calculate target ratios
    target_ratios = set(
        (i, j) for n in range(min_num, max_num + 1) 
        for i in range(1, n + 1) 
        for j in range(1, n + 1) 
        if i * j <= max_num and i * j >= min_num
    )
    target_ratios = sorted(target_ratios, key=lambda x: x[0] * x[1])

    # Find the closest aspect ratio
    target_aspect_ratio = find_closest_aspect_ratio(
        aspect_ratio, target_ratios, orig_width, orig_height, image_size
    )

    # Calculate target dimensions
    target_width = image_size * target_aspect_ratio[0]
    target_height = image_size * target_aspect_ratio[1]
    blocks = target_aspect_ratio[0] * target_aspect_ratio[1]

    # Resize the image
    resized_img = image.resize((target_width, target_height))
    processed_images = []
    
    for i in range(blocks):
        box = (
            (i % (target_width // image_size)) * image_size,
            (i // (target_width // image_size)) * image_size,
            ((i % (target_width // image_size)) + 1) * image_size,
            ((i // (target_width // image_size)) + 1) * image_size
        )
        split_img = resized_img.crop(box)
        processed_images.append(split_img)
    
    assert len(processed_images) == blocks
    
    if use_thumbnail and len(processed_images) != 1:
        thumbnail_img = image.resize((image_size, image_size))
        processed_images.append(thumbnail_img)
    
    return processed_images

def load_image(image_file, input_size=448, max_num=12):
    """Load image using official method"""
    image = Image.open(image_file).convert('RGB')
    transform = build_transform(input_size=input_size)
    images = dynamic_preprocess(image, image_size=input_size, use_thumbnail=True, max_num=max_num)
    pixel_values = [transform(image) for image in images]
    pixel_values = torch.stack(pixel_values)
    return pixel_values

def download_test_image():
    """Download test image"""
    try:
        url = "https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/bee.jpg"
        response = requests.get(url)
        image = Image.open(BytesIO(response.content))
        image.save("test_image_official.jpg")
        return True
    except Exception as e:
        print(f"❌ Error downloading image: {e}")
        return False

def test_internvl3_official():
    """Test InternVL3-8B using official example structure"""
    print("🧪 Testing InternVL3-8B with Official Code Structure")
    print("=" * 60)
    print("💾 Target: A40 GPU with 8-bit precision")
    
    # Check CUDA
    if not torch.cuda.is_available():
        print("❌ CUDA not available")
        return
    
    print(f"✅ CUDA available: {torch.cuda.get_device_name()}")
    
    # Model path
    path = 'OpenGVLab/InternVL3-8B'
    
    try:
        print("\n🤖 LOADING MODEL (Official Method)")
        print("-" * 40)
        
        # Load model exactly as in official example but with 8-bit for A40
        print("🔄 Loading model with A40-optimized settings...")
        model = AutoModel.from_pretrained(
            path,
            torch_dtype=torch.bfloat16,
            load_in_8bit=True,  # 8-bit for A40
            low_cpu_mem_usage=True,
            use_flash_attn=True,
            trust_remote_code=True
        ).eval()
        
        print("✅ Model loaded successfully")
        
        # Load tokenizer
        tokenizer = AutoTokenizer.from_pretrained(path, trust_remote_code=True, use_fast=False)
        print("✅ Tokenizer loaded successfully")
        
        # Check memory
        if torch.cuda.is_available():
            memory_allocated = torch.cuda.memory_allocated() / 1024**3
            memory_reserved = torch.cuda.memory_reserved() / 1024**3
            print(f"📊 A40 Memory - Allocated: {memory_allocated:.1f}GB, Reserved: {memory_reserved:.1f}GB")
        
    except Exception as e:
        print(f"❌ Model loading failed: {e}")
        return
    
    # Download test image
    print("\n📥 DOWNLOADING TEST IMAGE")
    print("-" * 40)
    
    if not download_test_image():
        print("❌ Failed to download test image")
        return
    
    print("✅ Test image downloaded")
    
    # Test 1: Pure text conversation
    print("\n🧪 TEST 1: Pure Text Conversation")
    print("-" * 40)
    
    try:
        generation_config = dict(max_new_tokens=200, do_sample=True, temperature=0.3)
        
        question = 'Hello, who are you?'
        print(f"Question: {question}")
        
        response, history = model.chat(
            tokenizer, 
            None, 
            question, 
            generation_config, 
            history=None, 
            return_history=True
        )
        
        print(f"Response: {response}")
        print("✅ Text conversation test successful!")
        
    except Exception as e:
        print(f"❌ Text conversation failed: {e}")
        return
    
    # Test 2: Single image conversation
    print("\n🧪 TEST 2: Single Image Conversation")
    print("-" * 40)
    
    try:
        # Load image using official method
        pixel_values = load_image('test_image_official.jpg', max_num=6)  # Reduced for A40
        pixel_values = pixel_values.to(torch.bfloat16).cuda()
        
        print(f"📊 Image processed: {pixel_values.shape}")
        
        # Single image conversation
        question = '<image>\nPlease describe this image shortly.'
        print(f"Question: {question}")
        
        response = model.chat(tokenizer, pixel_values, question, generation_config)
        print(f"Response: {response}")
        print("✅ Single image test successful!")
        
    except Exception as e:
        print(f"❌ Single image test failed: {e}")
        return
    
    # Test 3: Multi-round conversation
    print("\n🧪 TEST 3: Multi-round Image Conversation")
    print("-" * 40)
    
    try:
        # First question
        question1 = '<image>\nPlease describe the image in detail.'
        print(f"Question 1: {question1}")
        
        response1, history = model.chat(
            tokenizer, 
            pixel_values, 
            question1, 
            generation_config, 
            history=None, 
            return_history=True
        )
        print(f"Response 1: {response1}")
        
        # Follow-up question
        question2 = 'What colors do you see in this image?'
        print(f"Question 2: {question2}")
        
        response2, history = model.chat(
            tokenizer, 
            pixel_values, 
            question2, 
            generation_config, 
            history=history, 
            return_history=True
        )
        print(f"Response 2: {response2}")
        print("✅ Multi-round conversation test successful!")
        
    except Exception as e:
        print(f"❌ Multi-round conversation failed: {e}")
        return
    
    # Final memory check
    print("\n📊 FINAL A40 MEMORY CHECK")
    print("-" * 40)
    
    if torch.cuda.is_available():
        final_memory = torch.cuda.memory_reserved() / 1024**3
        print(f"📊 Final A40 usage: {final_memory:.1f}GB ({final_memory/48*100:.1f}% of 48GB)")
    
    print("\n🎉 All tests completed successfully!")
    print("✅ InternVL3-8B is working properly on A40 with 8-bit precision")
    print("\nℹ️ You can now run:")
    print("  python internvl3_complete_a40.py")

if __name__ == "__main__":
    test_internvl3_official()
