#!/usr/bin/env python3
"""
InternVL3-8B Memory-Optimized Inference
Ultra-conservative settings to avoid CUDA OOM
"""

import json
import time
import torch
import gc
import os
from datetime import datetime

# Set memory optimization environment variables
os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "expandable_segments:True"
torch.backends.cudnn.benchmark = False
torch.backends.cudnn.deterministic = True

# Import utilities
from utils.data_loader import load_prompts_from_jsonl
from utils.text_processor import remove_urls_from_text

def initialize_internvl3_minimal():
    """Initialize InternVL3 with minimal memory usage"""
    from transformers import AutoTokenizer, AutoModel
    
    model_id = "OpenGVLab/InternVL3-8B"
    
    print(f"🤖 Loading InternVL3 with minimal memory settings...")
    
    # Load tokenizer
    tokenizer = AutoTokenizer.from_pretrained(model_id, trust_remote_code=True, use_fast=False)
    
    # Load model with ultra-conservative settings
    model = AutoModel.from_pretrained(
        model_id,
        torch_dtype=torch.bfloat16,
        load_in_8bit=True,
        low_cpu_mem_usage=True,
        use_flash_attn=False,  # Disabled for memory
        trust_remote_code=True,
        device_map="auto",
        max_memory={"0": "15GB"}  # Very conservative limit
    ).eval()
    
    return model, tokenizer

def process_single_image_minimal(base64_data: str):
    """Process single image with minimal memory usage"""
    import base64
    from PIL import Image
    from io import BytesIO
    import torchvision.transforms as T
    
    try:
        # Decode image
        if base64_data.startswith('data:'):
            header, base64_content = base64_data.split(',', 1)
        else:
            base64_content = base64_data.strip()
        
        image_bytes = base64.b64decode(base64_content)
        image = Image.open(BytesIO(image_bytes)).convert('RGB')
        
        # Very aggressive resizing for memory
        max_size = 224  # Very small
        if max(image.size) > max_size:
            image.thumbnail((max_size, max_size), Image.Resampling.LANCZOS)
        
        # Simple transform - no dynamic preprocessing
        transform = T.Compose([
            T.Resize((224, 224)),
            T.ToTensor(),
            T.Normalize(mean=(0.485, 0.456, 0.406), std=(0.229, 0.224, 0.225))
        ])
        
        pixel_values = transform(image).unsqueeze(0)  # Single tile only
        print(f"    📏 Minimal processing: {pixel_values.shape}")
        return pixel_values
        
    except Exception as e:
        print(f"    ❌ Minimal processing error: {e}")
        return None

def main():
    """Memory-optimized InternVL3 inference"""
    print("🔋 InternVL3-8B Memory-Optimized Inference")
    print("=" * 60)
    print("⚠️ Ultra-conservative settings to avoid CUDA OOM")
    
    # Clear any existing GPU memory
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        gc.collect()
    
    # Load prompts
    input_files = ['final_image_prompts_cleaned.jsonl', 'scrape_content_prompts.jsonl']
    prompts = None
    
    for input_file in input_files:
        prompts = load_prompts_from_jsonl(input_file)
        if prompts:
            print(f"✅ Using prompts from: {input_file}")
            break
    
    if not prompts:
        print("❌ No prompts found")
        return
    
    # Initialize model
    try:
        model, tokenizer = initialize_internvl3_minimal()
        print("✅ Model loaded with minimal settings")
    except Exception as e:
        print(f"❌ Model loading failed: {e}")
        return
    
    # Process only 3 prompts to avoid memory accumulation
    results = []
    max_prompts = min(len(prompts), 3)
    
    print(f"\n🚀 PROCESSING {max_prompts} PROMPTS (MEMORY OPTIMIZED)")
    print("=" * 60)
    
    for i, prompt_data in enumerate(prompts[:max_prompts], 1):
        print(f"\n{'='*15} PROMPT {i}/{max_prompts} {'='*15}")
        
        # Clear memory before each prompt
        torch.cuda.empty_cache()
        gc.collect()
        
        try:
            # Extract and clean data
            system_prompt = prompt_data.get('SystemPrompt', '')
            user_prompt = prompt_data.get('UserPrompt', '')
            image_data_urls = prompt_data.get('Images', [])
            
            # Remove URLs
            cleaned_system, system_urls = remove_urls_from_text(system_prompt)
            cleaned_user, user_urls = remove_urls_from_text(user_prompt)
            
            print(f"🔗 URLs removed: {system_urls + user_urls}")
            
            # Process only ONE image with minimal settings
            if not image_data_urls:
                print("❌ No images, skipping")
                continue
            
            print("🖼️ Processing first image only (minimal)...")
            pixel_values = process_single_image_minimal(image_data_urls[0])
            
            if pixel_values is None:
                print("❌ Image processing failed")
                continue
            
            # Move to GPU
            device = next(model.parameters()).device
            pixel_values = pixel_values.to(torch.bfloat16).to(device)
            
            # Create very short prompt to save memory
            short_prompt = f"{cleaned_system[:500]}...\n\n{cleaned_user[:500]}..."
            question = f"<image>\n{short_prompt}"
            
            print(f"📝 Using shortened prompt: {len(question)} chars")
            
            # Minimal generation config
            generation_config = {
                'max_new_tokens': 500,  # Very short response
                'temperature': 0.1,
                'do_sample': False  # Greedy for memory efficiency
            }
            
            # Run inference with maximum memory safety
            print("🚀 Running minimal inference...")
            start_time = time.time()
            
            with torch.no_grad():
                response = model.chat(tokenizer, pixel_values, question, generation_config)
            
            inference_time = time.time() - start_time
            
            # Immediate cleanup
            del pixel_values
            torch.cuda.empty_cache()
            gc.collect()
            
            print(f"✅ Success in {inference_time:.2f}s")
            print(f"📝 Generated: {len(response)} chars")
            print(f"📄 Preview: {response[:200]}...")
            
            results.append({
                'prompt_id': f"prompt_{i}",
                'model': "InternVL3-8B-minimal",
                'mode': 'memory_optimized',
                'urls_removed': system_urls + user_urls,
                'generated_response': response,
                'generated_length': len(response),
                'inference_time': inference_time,
                'success': True
            })
            
        except torch.cuda.OutOfMemoryError as e:
            print(f"❌ CUDA OOM even with minimal settings: {e}")
            torch.cuda.empty_cache()
            gc.collect()
            
            results.append({
                'prompt_id': f"prompt_{i}",
                'success': False,
                'error': f"CUDA OOM: {str(e)}"
            })
            
        except Exception as e:
            print(f"❌ Error: {e}")
            torch.cuda.empty_cache()
            gc.collect()
            
            results.append({
                'prompt_id': f"prompt_{i}",
                'success': False,
                'error': str(e)
            })
        
        # Memory status
        if torch.cuda.is_available():
            memory_allocated = torch.cuda.memory_allocated() / 1024**3
            print(f"💾 GPU memory after prompt {i}: {memory_allocated:.1f}GB")
    
    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f'internvl3_minimal_results_{timestamp}.json'
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    # Summary
    successful = [r for r in results if r.get('success', False)]
    
    print(f"\n📊 MEMORY-OPTIMIZED SUMMARY:")
    print("=" * 60)
    print(f"🤖 Model: InternVL3-8B (minimal config)")
    print(f"📊 Processed: {len(results)} prompts")
    print(f"✅ Successful: {len(successful)}")
    print(f"🔋 Mode: Ultra-conservative memory")
    
    if successful:
        avg_time = sum(r['inference_time'] for r in successful) / len(successful)
        avg_length = sum(r['generated_length'] for r in successful) / len(successful)
        
        print(f"⏱️ Average time: {avg_time:.2f}s")
        print(f"📝 Average response: {avg_length:.0f} chars")
    
    print(f"\n💾 Results saved to: {results_file}")
    print("🎉 Memory-optimized inference completed!")
    
    if len(successful) < len(results):
        print("\n💡 If still getting OOM errors:")
        print("1. Reduce max_memory limit further")
        print("2. Use CPU inference: device_map='cpu'")
        print("3. Process one prompt at a time")

if __name__ == "__main__":
    main()
