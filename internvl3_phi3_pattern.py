#!/usr/bin/env python3
"""
InternVL3-8B Inference Following Phi-3-Vision Pattern
Exact same prompt structure and message format as Phi-3-Vision
"""

import json
import time
import torch
import torchvision.transforms as T
from datetime import datetime
from io import BytesIO
from PIL import Image
from torchvision.transforms.functional import InterpolationMode
from transformers import AutoModel, AutoTokenizer
import base64
import re

# Import existing utilities
from utils.data_loader import load_prompts_from_jsonl

# Constants from official InternVL3 example
IMAGENET_MEAN = (0.485, 0.456, 0.406)
IMAGENET_STD = (0.229, 0.224, 0.225)

def build_transform(input_size):
    """Build image transformation pipeline"""
    MEAN, STD = IMAGENET_MEAN, IMAGENET_STD
    transform = T.Compose([
        T.Lambda(lambda img: img.convert('RGB') if img.mode != 'RGB' else img),
        T.Resize((input_size, input_size), interpolation=InterpolationMode.BICUBIC),
        T<PERSON>(),
        T.Normalize(mean=MEAN, std=STD)
    ])
    return transform

def find_closest_aspect_ratio(aspect_ratio, target_ratios, width, height, image_size):
    """Find the closest aspect ratio from target ratios"""
    best_ratio_diff = float('inf')
    best_ratio = (1, 1)
    area = width * height
    for ratio in target_ratios:
        target_aspect_ratio = ratio[0] / ratio[1]
        ratio_diff = abs(aspect_ratio - target_aspect_ratio)
        if ratio_diff < best_ratio_diff:
            best_ratio_diff = ratio_diff
            best_ratio = ratio
        elif ratio_diff == best_ratio_diff:
            if area > 0.5 * image_size * image_size * ratio[0] * ratio[1]:
                best_ratio = ratio
    return best_ratio

def dynamic_preprocess(image, min_num=1, max_num=12, image_size=448, use_thumbnail=False):
    """Dynamic preprocessing for InternVL3"""
    orig_width, orig_height = image.size
    aspect_ratio = orig_width / orig_height

    target_ratios = set(
        (i, j) for n in range(min_num, max_num + 1) 
        for i in range(1, n + 1) 
        for j in range(1, n + 1) 
        if i * j <= max_num and i * j >= min_num
    )
    target_ratios = sorted(target_ratios, key=lambda x: x[0] * x[1])

    target_aspect_ratio = find_closest_aspect_ratio(
        aspect_ratio, target_ratios, orig_width, orig_height, image_size
    )

    target_width = image_size * target_aspect_ratio[0]
    target_height = image_size * target_aspect_ratio[1]
    blocks = target_aspect_ratio[0] * target_aspect_ratio[1]

    resized_img = image.resize((target_width, target_height))
    processed_images = []
    
    for i in range(blocks):
        box = (
            (i % (target_width // image_size)) * image_size,
            (i // (target_width // image_size)) * image_size,
            ((i % (target_width // image_size)) + 1) * image_size,
            ((i // (target_width // image_size)) + 1) * image_size
        )
        split_img = resized_img.crop(box)
        processed_images.append(split_img)
    
    assert len(processed_images) == blocks
    
    if use_thumbnail and len(processed_images) != 1:
        thumbnail_img = image.resize((image_size, image_size))
        processed_images.append(thumbnail_img)
    
    return processed_images

def load_image_from_base64(base64_data, input_size=448, max_num=6):
    """Load image from base64 data using InternVL3 preprocessing"""
    try:
        if base64_data.startswith('data:'):
            header, base64_content = base64_data.split(',', 1)
        else:
            base64_content = base64_data.strip()

        image_bytes = base64.b64decode(base64_content)
        image = Image.open(BytesIO(image_bytes)).convert('RGB')
        
        transform = build_transform(input_size=input_size)
        images = dynamic_preprocess(
            image, 
            image_size=input_size, 
            use_thumbnail=True, 
            max_num=max_num
        )
        pixel_values = [transform(img) for img in images]
        pixel_values = torch.stack(pixel_values)
        
        print(f"    📏 Processed into {len(images)} tiles: {pixel_values.shape}")
        return pixel_values
        
    except Exception as e:
        print(f"    ❌ Image processing error: {e}")
        return None

def remove_urls_from_text(text: str):
    """Remove URLs from text to reduce token count"""
    url_patterns = [
        r'https?://[^\s<>"]+',
        r'www\.[^\s<>"]+',
        r'ftp://[^\s<>"]+',
        r'[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(?:/[^\s<>"]*)?',
    ]
    
    cleaned_text = text
    urls_removed = 0
    
    for pattern in url_patterns:
        matches = re.findall(pattern, cleaned_text)
        urls_removed += len(matches)
        cleaned_text = re.sub(pattern, '', cleaned_text)
    
    cleaned_text = re.sub(r'\s+', ' ', cleaned_text)
    cleaned_text = re.sub(r'\n\s*\n\s*\n', '\n\n', cleaned_text)
    cleaned_text = cleaned_text.strip()
    
    return cleaned_text, urls_removed

def main():
    """InternVL3-8B inference following exact Phi-3-Vision pattern"""
    print("🌟 InternVL3-8B Inference (Phi-3-Vision Pattern)")
    print("=" * 60)
    print("🎯 Exact same prompt structure as Phi-3-Vision")
    
    # Load prompts
    input_files = ['final_image_prompts_cleaned.jsonl', 'scrape_content_prompts.jsonl']
    prompts = None
    
    for input_file in input_files:
        prompts = load_prompts_from_jsonl(input_file)
        if prompts:
            print(f"✅ Using prompts from: {input_file}")
            break
    
    if not prompts:
        print("❌ No prompts found")
        return
    
    # Initialize InternVL3-8B model
    print(f"\n🤖 INITIALIZING INTERNVL3-8B")
    print("-" * 40)
    
    try:
        model_path = 'OpenGVLab/InternVL3-8B'
        
        tokenizer = AutoTokenizer.from_pretrained(
            model_path, 
            trust_remote_code=True, 
            use_fast=False
        )
        print("✅ Tokenizer loaded successfully")
        
        model = AutoModel.from_pretrained(
            model_path,
            torch_dtype=torch.bfloat16,
            load_in_8bit=True,
            low_cpu_mem_usage=True,
            use_flash_attn=True,
            trust_remote_code=True,
            device_map="auto"
        ).eval()
        
        print("✅ Model loaded successfully")
        
    except Exception as e:
        print(f"❌ Model initialization failed: {e}")
        return
    
    # Generation config (same as Phi-3-Vision)
    generation_config = {
        'max_new_tokens': 2000,
        'temperature': 0.3,
        'do_sample': True,
        'top_p': 0.9
    }
    
    # Process prompts
    results = []
    max_prompts = min(len(prompts), 5)
    
    print(f"\n🚀 PROCESSING {max_prompts} PROMPTS")
    print("=" * 60)
    
    for i, prompt_data in enumerate(prompts[:max_prompts], 1):
        print(f"\n{'='*15} PROMPT {i}/{max_prompts} {'='*15}")
        
        try:
            # Extract data (same as Phi-3-Vision)
            system_prompt = prompt_data.get('SystemPrompt', '')
            user_prompt = prompt_data.get('UserPrompt', '')
            image_data_urls = prompt_data.get('Images', [])
            
            print(f"📋 Original lengths:")
            print(f"  System: {len(system_prompt):,} chars")
            print(f"  User: {len(user_prompt):,} chars")
            print(f"  Images: {len(image_data_urls)}")
            
            # Remove URLs (same as Phi-3-Vision)
            cleaned_system, system_urls = remove_urls_from_text(system_prompt)
            cleaned_user, user_urls = remove_urls_from_text(user_prompt)
            
            total_urls = system_urls + user_urls
            total_saved = (len(system_prompt) - len(cleaned_system)) + (len(user_prompt) - len(cleaned_user))
            
            print(f"🔗 URLs removed: {total_urls}, Chars saved: {total_saved:,}")
            
            # Process images
            processed_images = []
            print(f"🖼️ Processing images...")
            
            for j, img_data in enumerate(image_data_urls[:3]):
                pixel_values = load_image_from_base64(img_data, input_size=448, max_num=6)
                if pixel_values is not None:
                    processed_images.append(pixel_values)
                    print(f"  ✓ Image {j+1}: {pixel_values.shape}")
                else:
                    print(f"  ✗ Image {j+1}: failed")
            
            if not processed_images:
                print("❌ No valid images, skipping prompt")
                continue
            
            # Combine images
            if len(processed_images) == 1:
                combined_pixel_values = processed_images[0]
                num_patches_list = [processed_images[0].size(0)]
            else:
                combined_pixel_values = torch.cat(processed_images, dim=0)
                num_patches_list = [img.size(0) for img in processed_images]
            
            combined_pixel_values = combined_pixel_values.to(torch.bfloat16).cuda()
            
            # Create message EXACTLY like Phi-3-Vision (system + user combined)
            if len(processed_images) == 1:
                full_prompt = f"<image>\n{cleaned_system}\n\n{cleaned_user}"
            else:
                image_prefix = "".join([f"Image-{j+1}: <image>\n" for j in range(len(processed_images))])
                full_prompt = f"{image_prefix}{cleaned_system}\n\n{cleaned_user}"
            
            print(f"📝 Full prompt length: {len(full_prompt):,} chars")
            
            # Run inference using InternVL3 chat method
            print("🚀 Running InternVL3 inference...")
            start_time = time.time()
            
            if len(processed_images) == 1:
                response = model.chat(
                    tokenizer, 
                    combined_pixel_values, 
                    full_prompt, 
                    generation_config
                )
            else:
                response = model.chat(
                    tokenizer, 
                    combined_pixel_values, 
                    full_prompt, 
                    generation_config,
                    num_patches_list=num_patches_list
                )
            
            inference_time = time.time() - start_time
            
            print(f"✅ Inference completed in {inference_time:.2f}s")
            print(f"📝 Generated: {len(response):,} chars")
            print(f"📄 Preview: {response[:200]}...")
            
            # Store results (same structure as Phi-3-Vision)
            result = {
                'prompt_id': f"prompt_{i}",
                'model': "OpenGVLab/InternVL3-8B",
                'pattern': "phi3_vision_style",
                'urls_removed': total_urls,
                'chars_saved': total_saved,
                'num_images': len(processed_images),
                'total_patches': sum(num_patches_list),
                'generated_response': response,
                'generated_length': len(response),
                'inference_time': inference_time,
                'temperature': generation_config['temperature'],
                'success': True
            }
            
            results.append(result)
            
        except Exception as e:
            print(f"❌ Error processing prompt {i}: {e}")
            results.append({
                'prompt_id': f"prompt_{i}",
                'success': False,
                'error': str(e)
            })
    
    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f'internvl3_phi3_pattern_results_{timestamp}.json'
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    # Summary
    successful = [r for r in results if r.get('success', False)]
    
    print(f"\n📊 SUMMARY (Phi-3-Vision Pattern):")
    print("=" * 60)
    print(f"🤖 Model: InternVL3-8B")
    print(f"📋 Pattern: Phi-3-Vision style prompts")
    print(f"📊 Processed: {len(results)} prompts")
    print(f"✅ Successful: {len(successful)}")
    
    if successful:
        avg_time = sum(r['inference_time'] for r in successful) / len(successful)
        total_urls = sum(r['urls_removed'] for r in successful)
        total_saved = sum(r['chars_saved'] for r in successful)
        avg_length = sum(r['generated_length'] for r in successful) / len(successful)
        
        print(f"⏱️ Average time: {avg_time:.2f}s")
        print(f"🔗 Total URLs removed: {total_urls:,}")
        print(f"💾 Total chars saved: {total_saved:,}")
        print(f"📝 Average response: {avg_length:,.0f} chars")
    
    print(f"\n💾 Results saved to: {results_file}")
    print("🎉 InternVL3-8B inference completed (Phi-3-Vision pattern)!")

if __name__ == "__main__":
    main()
