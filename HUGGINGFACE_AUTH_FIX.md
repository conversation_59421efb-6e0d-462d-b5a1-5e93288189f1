# Hugging Face Authentication Fix

## 🚨 **Error Explanation**

### **The Problem:**
```
❌ Error: You are trying to access a gated repo.
Access to model google/gemma-3n-E2B-it is restricted.
You must have access to it and be authenticated to access it.
```

### **Root Cause:**
- **Gemma-3n is a gated model** - requires approval
- **Authentication required** - need Hugging Face token
- **Access request needed** - must request access to the model

## 🔧 **Quick Fix (3 Steps)**

### **Step 1: Get Hugging Face Token**
1. Go to: https://huggingface.co/settings/tokens
2. Click **"New token"**
3. Name: `VLM_Inference`
4. Type: **"Read"** (sufficient for model access)
5. Click **"Generate"**
6. **Copy the token** (starts with `hf_...`)

### **Step 2: Request Model Access**
1. Go to: https://huggingface.co/google/gemma-3n-e2b-it
2. Click **"Request access"** or **"Agree and access repository"**
3. Fill out any required forms
4. **Wait for approval** (usually within minutes)

### **Step 3: Set Up Authentication**
Choose one of these methods:

#### **Option A: Environment Variable (Recommended)**
```bash
# Linux/Mac
export HF_TOKEN="hf_your_token_here"

# Windows
set HF_TOKEN=hf_your_token_here

# Or add to ~/.bashrc for permanent setup
echo 'export HF_TOKEN="hf_your_token_here"' >> ~/.bashrc
source ~/.bashrc
```

#### **Option B: Use Setup Script**
```bash
python setup_huggingface_auth.py
```

#### **Option C: Interactive Login**
```bash
# The script will prompt for token automatically
python test_gemma3n.py
```

## 🚀 **Automated Setup Script**

### **I've created `setup_huggingface_auth.py` that:**
- ✅ **Checks current authentication**
- ✅ **Guides through token setup**
- ✅ **Performs interactive login**
- ✅ **Verifies model access**
- ✅ **Provides clear instructions**

### **Run the setup:**
```bash
python setup_huggingface_auth.py

# Expected output:
🔐 Hugging Face Authentication Setup
==================================================

📋 STEP 1: Check Current Authentication
❌ Not authenticated

📋 STEP 2: Authentication Required
🔧 ENVIRONMENT TOKEN SETUP
1. Go to: https://huggingface.co/settings/tokens
2. Click 'New token'
3. Type: 'Read'
4. Copy the generated token

🔐 INTERACTIVE LOGIN
📝 Enter your Hugging Face token: hf_...
✅ Authentication successful!

📋 STEP 3: Check Model Access
✅ Access confirmed to: google/gemma-3n-e2b-it

🎉 SETUP COMPLETE!
✅ Hugging Face authentication: OK
✅ Gemma-3n model access: OK
```

## 🔄 **Updated Model Initializer**

### **Enhanced `utils/gemma_model_initializer.py`:**
- ✅ **Automatic auth check** before loading model
- ✅ **Environment token support** (HF_TOKEN)
- ✅ **Interactive login fallback**
- ✅ **Clear error messages** for gated repos
- ✅ **Helpful troubleshooting** guidance

### **New Functions Added:**
```python
def check_huggingface_auth():
    """Check if user is authenticated"""

def huggingface_login():
    """Handle authentication with multiple methods"""

def initialize_gemma3n():
    """Initialize with automatic authentication"""
```

## 📋 **Step-by-Step Solution**

### **1. Quick Token Setup:**
```bash
# Get your token from https://huggingface.co/settings/tokens
export HF_TOKEN="hf_your_token_here"
```

### **2. Request Model Access:**
```bash
# Visit https://huggingface.co/google/gemma-3n-e2b-it
# Click "Request access"
# Wait for approval
```

### **3. Test Authentication:**
```bash
python setup_huggingface_auth.py
```

### **4. Run Gemma-3n:**
```bash
python test_gemma3n.py
```

## 🎯 **Expected Success Flow**

### **After Setup:**
```bash
python test_gemma3n.py

🧪 Testing Gemma-3n Model
========================================
🤖 Initializing Gemma-3n model: google/gemma-3n-e2b-it
🔐 Checking Hugging Face authentication...
✅ Already authenticated with Hugging Face
📊 Loading model and processor...
✅ Processor loaded successfully
✅ Model loaded successfully
💾 Device: cuda:0
🔢 Dtype: torch.bfloat16

📥 Downloading test image...
✅ Test image loaded: (1024, 683)
🚀 Running test inference...
✅ Test completed!
📝 Generated Response:
The image shows a close-up of a vibrant yellow flower...
```

## 🛠️ **Troubleshooting**

### **Common Issues:**

#### **1. "Token not found"**
```bash
# Solution: Set environment variable
export HF_TOKEN="hf_your_token_here"
```

#### **2. "Access denied"**
```bash
# Solution: Request model access
# Visit: https://huggingface.co/google/gemma-3n-e2b-it
# Click "Request access"
```

#### **3. "Invalid token"**
```bash
# Solution: Generate new token
# Visit: https://huggingface.co/settings/tokens
# Create new token with "Read" permissions
```

#### **4. "Still getting 401 error"**
```bash
# Solution: Clear cache and re-authenticate
rm -rf ~/.cache/huggingface/
python setup_huggingface_auth.py
```

## 🎉 **Summary**

### **The Fix:**
1. ✅ **Enhanced authentication** in model initializer
2. ✅ **Setup script** for guided authentication
3. ✅ **Environment token support** for automation
4. ✅ **Clear error messages** for troubleshooting

### **What You Need:**
1. **Hugging Face account** (free)
2. **Access token** (Read permissions)
3. **Model access approval** (usually instant)

### **Commands to Run:**
```bash
# 1. Set up authentication
python setup_huggingface_auth.py

# 2. Test the model
python test_gemma3n.py

# 3. Run full inference
python gemma3n_inference.py
```

**After following these steps, your Gemma-3n inference will work perfectly!** 🚀
