#!/usr/bin/env python3
"""
vLLM VLM Inference with Paged Attention
Efficient multi-image inference using vLLM's optimized VLM support
"""

import json
import time
import base64
import difflib
from io import BytesIO
from typing import List, Dict, Any
import asyncio
from datetime import datetime

from PIL import Image
from vllm import LLM, SamplingParams
from vllm.multimodal.utils import encode_image_base64

# vLLM supported VLM models with their context lengths
SUPPORTED_VLM_MODELS = {
    "llava-1.5-7b": {
        "model_id": "llava-hf/llava-1.5-7b-hf",
        "max_tokens": 4096,
        "recommended": False
    },
    "llava-v1.6-mistral-7b": {
        "model_id": "llava-hf/llava-v1.6-mistral-7b-hf",
        "max_tokens": 32768,  # Mistral-7B base has 32k context
        "recommended": True
    },
    "llava-v1.6-vicuna-13b": {
        "model_id": "llava-hf/llava-v1.6-vicuna-13b-hf",
        "max_tokens": 4096,
        "recommended": False
    },
    "phi3-vision": {
        "model_id": "microsoft/Phi-3-vision-128k-instruct",
        "max_tokens": 131072,  # 128k context!
        "recommended": True
    },
    "internvl2-8b": {
        "model_id": "OpenGVLab/InternVL2-8B",
        "max_tokens": 8192,
        "recommended": True
    },
    "qwen2-vl-7b": {
        "model_id": "Qwen/Qwen2-VL-7B-Instruct",
        "max_tokens": 32768,  # 32k context
        "recommended": True
    },
    "qwen2-vl-2b": {
        "model_id": "Qwen/Qwen2-VL-2B-Instruct",
        "max_tokens": 32768,  # 32k context, smaller model
        "recommended": True
    }
}

def remove_urls_from_text(text: str) -> tuple:
    """Remove all URLs from text to reduce token count and maximize context usage"""
    import re

    # Remove various URL patterns
    url_patterns = [
        r'https?://[^\s<>"]+',  # Standard HTTP/HTTPS URLs
        r'www\.[^\s<>"]+',      # www URLs without protocol
        r'ftp://[^\s<>"]+',     # FTP URLs
        r'[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(?:/[^\s<>"]*)?',  # Domain-like patterns
    ]

    cleaned_text = text
    urls_removed = 0

    for pattern in url_patterns:
        matches = re.findall(pattern, cleaned_text)
        urls_removed += len(matches)
        cleaned_text = re.sub(pattern, '', cleaned_text)

    # Clean up extra whitespace left by URL removal
    cleaned_text = re.sub(r'\s+', ' ', cleaned_text)  # Multiple spaces to single
    cleaned_text = re.sub(r'\n\s*\n\s*\n', '\n\n', cleaned_text)  # Multiple newlines
    cleaned_text = cleaned_text.strip()

    return cleaned_text, urls_removed

def load_prompts_from_jsonl(file_path: str) -> List[Dict[str, Any]]:
    """Load prompts from JSONL file"""
    print(f"📂 Loading prompts from {file_path}...")

    try:
        prompts = []
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue

                try:
                    prompt_data = json.loads(line)
                    prompts.append(prompt_data)
                except json.JSONDecodeError as e:
                    print(f"⚠️ Skipping invalid JSON on line {line_num}: {str(e)}")
                    continue

        print(f"✅ Loaded {len(prompts)} prompts from JSONL")
        return prompts

    except Exception as e:
        print(f"❌ Error loading prompts: {str(e)}")
        return []

def prepare_image_from_base64(base64_data: str) -> Image.Image:
    """Convert base64 image data to PIL Image for vLLM"""
    try:
        # Remove data URL prefix if present
        if base64_data.startswith('data:'):
            header, base64_content = base64_data.split(',', 1)
        else:
            base64_content = base64_data.strip()

        # Decode image
        try:
            image_bytes = base64.b64decode(base64_content)
        except Exception as decode_error:
            print(f"    ⚠️ Base64 decode error: {str(decode_error)}")
            return None

        # Open image
        try:
            image = Image.open(BytesIO(image_bytes))
        except Exception as image_error:
            print(f"    ⚠️ Image open error: {str(image_error)}")
            return None

        # Convert to RGB if needed
        if image.mode != 'RGB':
            image = image.convert('RGB')

        # Resize if too large (vLLM memory optimization)
        max_size = 768  # Reduced for better memory usage
        if max(image.size) > max_size:
            original_size = image.size
            image.thumbnail((max_size, max_size), Image.Resampling.LANCZOS)
            print(f"    📏 Resized image: {original_size} → {image.size}")

        return image

    except Exception as e:
        print(f"    ⚠️ Error processing image: {str(e)}")
        return None

def create_vllm_messages(system_prompt: str, user_prompt: str, images: List[str]) -> List[Dict]:
    """Create messages in vLLM format"""
    # Combine system and user prompts
    full_text = f"{system_prompt}\n\n{user_prompt}"

    # Create message content with images and text
    content = []

    # Add images first
    for i, image_data in enumerate(images):
        if image_data:
            content.append({
                "type": "image_url",
                "image_url": {"url": image_data}
            })

    # Add text
    content.append({
        "type": "text",
        "text": full_text
    })

    return [{"role": "user", "content": content}]

def create_model_specific_prompt(model_name: str, system_prompt: str, user_prompt: str, num_images: int) -> str:
    """Create model-specific prompt format with proper image tokens"""

    # Create image tokens based on model type
    if "llava" in model_name.lower():
        # LLaVA models need <image> token for each image
        image_tokens = "<image>\n" * num_images
        return f"USER: {image_tokens}{system_prompt}\n\n{user_prompt}\nASSISTANT:"

    elif "phi3" in model_name.lower() or "phi-3" in model_name.lower():
        # Phi-3 Vision uses <|image_1|>, <|image_2|>, etc.
        image_tokens = "".join([f"<|image_{i+1}|>" for i in range(num_images)])
        return f"<|user|>\n{image_tokens}\n{system_prompt}\n\n{user_prompt}<|end|>\n<|assistant|>\n"

    elif "qwen2-vl" in model_name.lower():
        # Qwen2-VL uses <img> tokens
        image_tokens = "<img>" * num_images
        return f"<|im_start|>user\n{image_tokens}{system_prompt}\n\n{user_prompt}<|im_end|>\n<|im_start|>assistant\n"

    elif "internvl" in model_name.lower():
        # InternVL uses <image> tokens
        image_tokens = "<image>\n" * num_images
        return f"User: {image_tokens}{system_prompt}\n\n{user_prompt}\nAssistant:"

    else:
        # Default to LLaVA format
        image_tokens = "<image>\n" * num_images
        return f"USER: {image_tokens}{system_prompt}\n\n{user_prompt}\nASSISTANT:"

def get_model_stop_tokens(model_name: str) -> list:
    """Get appropriate stop tokens for different models"""

    if "llava" in model_name.lower():
        return ["</s>", "<|endoftext|>", "USER:", "ASSISTANT:"]

    elif "phi3" in model_name.lower() or "phi-3" in model_name.lower():
        return ["<|end|>", "<|endoftext|>", "<|user|>", "<|assistant|>"]

    elif "qwen2-vl" in model_name.lower():
        return ["<|im_end|>", "<|endoftext|>", "<|im_start|>"]

    elif "internvl" in model_name.lower():
        return ["</s>", "<|endoftext|>", "User:", "Assistant:"]

    else:
        # Default stop tokens
        return ["</s>", "<|endoftext|>", "USER:", "ASSISTANT:"]

def initialize_vllm_model(model_name: str = "qwen2-vl-7b", max_images: int = 5) -> LLM:
    """Initialize vLLM model with optimized settings for long context"""

    if model_name not in SUPPORTED_VLM_MODELS:
        print(f"⚠️ Model {model_name} not found, using default")
        model_name = "qwen2-vl-7b"

    model_config = SUPPORTED_VLM_MODELS[model_name]
    model_id = model_config["model_id"]
    max_model_len = model_config["max_tokens"]

    print(f"🤖 Initializing vLLM model: {model_id}")
    print(f"📊 Configuration:")
    print(f"  - Model: {model_name}")
    print(f"  - Max context length: {max_model_len:,} tokens")
    print(f"  - Max images per request: {max_images}")
    print(f"  - Using paged attention: Yes")
    print(f"  - Recommended: {'✅' if model_config['recommended'] else '❌'}")

    try:
        # Initialize vLLM with model-specific settings
        # For Phi-3-Vision, use much larger context but be conservative
        if "phi3" in model_name.lower() or "phi-3" in model_name.lower():
            # Try progressively smaller contexts until one works - start with full 128k after URL removal
            context_sizes = [128000, 120000, 100000, 80000, 65536, 50000, 40000, 30000]
            actual_max_len = None

            for context_size in context_sizes:
                try:
                    print(f"  Trying {context_size:,} token context...")
                    test_llm = LLM(
                        model=model_id,
                        trust_remote_code=True,
                        max_model_len=context_size,
                        gpu_memory_utilization=0.7,
                        swap_space=8,
                        enforce_eager=False,
                        max_num_seqs=1,
                        limit_mm_per_prompt={"image": max_images},
                        # Note: Paged attention is enabled by default in modern vLLM
                    )
                    actual_max_len = context_size
                    llm = test_llm
                    print(f"  ✅ Successfully loaded with {context_size:,} tokens")
                    break
                except Exception as e:
                    print(f"  ❌ {context_size:,} tokens failed: {str(e)[:100]}...")
                    continue

            if actual_max_len is None:
                raise Exception("Could not initialize Phi-3-Vision with any context size")

            gpu_util = 0.7
        else:
            actual_max_len = min(max_model_len, 16384)  # Cap at 16k for other models
            gpu_util = 0.8

            llm = LLM(
                model=model_id,
                trust_remote_code=True,
                max_model_len=actual_max_len,
                gpu_memory_utilization=gpu_util,
                swap_space=6,
                enforce_eager=False,
                max_num_seqs=1,
                limit_mm_per_prompt={"image": max_images},
                # Note: Paged attention is enabled by default in modern vLLM
            )
        print(f"✅ vLLM model initialized successfully")
        print(f"📏 Using context length: {actual_max_len:,} tokens")
        return llm

    except Exception as e:
        print(f"❌ vLLM model initialization failed: {str(e)}")
        print("\n🔧 Troubleshooting:")
        print("1. Try a different model:")
        for name, config in SUPPORTED_VLM_MODELS.items():
            if config["recommended"]:
                print(f"   - {name} (max: {config['max_tokens']:,} tokens)")
        print("2. Check GPU memory")
        print("3. Install latest vLLM: pip install --upgrade vllm")
        raise

def run_vllm_inference(llm: LLM, prompts_data: List[Dict], max_prompts: int = 5, model_name: str = "llava-1.5-7b") -> List[Dict]:
    """Run inference using vLLM with paged attention"""

    # Limit prompts for testing
    prompts_data = prompts_data[:max_prompts]

    print(f"\n🚀 Starting vLLM inference on {len(prompts_data)} prompts")
    print("="*60)

    results = []

    # Process prompts one by one for better error handling
    for i, prompt_data in enumerate(prompts_data, 1):
        print(f"\n📋 Processing prompt {i}/{len(prompts_data)}:")

        try:
            # Extract data
            system_prompt = prompt_data.get('SystemPrompt', '')
            user_prompt = prompt_data.get('UserPrompt', '')
            image_data_urls = prompt_data.get('Images', [])
            actual_response = prompt_data.get('Response', '')

            print(f"  System: {len(system_prompt)} chars")
            print(f"  User: {len(user_prompt)} chars")
            print(f"  Images: {len(image_data_urls)}")

            # Remove URLs first to maximize available context
            print(f"  🔗 Removing URLs to maximize context...")
            cleaned_system, system_urls = remove_urls_from_text(system_prompt)
            cleaned_user, user_urls = remove_urls_from_text(user_prompt)

            total_urls_removed = system_urls + user_urls
            total_chars_saved = (len(system_prompt) - len(cleaned_system)) + (len(user_prompt) - len(cleaned_user))

            print(f"  📝 URL removal results:")
            print(f"    URLs removed: {total_urls_removed}")
            print(f"    Characters saved: {total_chars_saved:,}")
            print(f"    System: {len(system_prompt):,} → {len(cleaned_system):,} chars")
            print(f"    User: {len(user_prompt):,} → {len(cleaned_user):,} chars")

            # Use cleaned prompts
            system_prompt = cleaned_system
            user_prompt = cleaned_user

            # Process images - reduce count to save tokens for text
            max_images_to_process = 2  # Reduced from 3 to save tokens for full text
            processed_images = []

            print(f"  🖼️ Processing {min(len(image_data_urls), max_images_to_process)} of {len(image_data_urls)} images")

            for j, img_data in enumerate(image_data_urls[:max_images_to_process]):
                processed_img = prepare_image_from_base64(img_data)
                if processed_img:
                    processed_images.append(processed_img)
                    print(f"    ✓ Image {j+1}: processed")
                else:
                    print(f"    ✗ Image {j+1}: failed")

            if not processed_images:
                print(f"  ⚠️ No valid images, skipping prompt {i}")
                continue

            # Smart truncation based on actual model context
            total_chars = len(system_prompt) + len(user_prompt)

            # Conservative token estimation (images take more tokens than estimated)
            estimated_text_tokens = total_chars / 2.5  # More conservative
            estimated_image_tokens = len(processed_images) * 1000  # Higher estimate
            estimated_total = estimated_text_tokens + estimated_image_tokens

            print(f"  🔢 Conservative estimate: {estimated_total:,.0f} tokens")
            print(f"      Available context: {actual_max_len:,} tokens")

            # Apply truncation if needed (leave 20% buffer for safety)
            safe_context_limit = actual_max_len * 0.8

            if estimated_total > safe_context_limit:
                print(f"  ⚠️ Applying aggressive truncation for safety...")

                # Reserve tokens for images and response
                reserved_tokens = len(processed_images) * 1000 + 2000
                available_for_text = safe_context_limit - reserved_tokens
                target_chars = int(available_for_text * 2.5)  # Convert back to chars

                if target_chars < 1000:
                    print(f"  ❌ Not enough context even after truncation")
                    continue

                # Proportional truncation
                system_ratio = 0.3  # Give less to system to preserve user content
                user_ratio = 0.7

                system_target = int(target_chars * system_ratio)
                user_target = int(target_chars * user_ratio)

                # Smart system prompt truncation
                if len(system_prompt) > system_target:
                    if "INSTRUCTIONS:" in system_prompt:
                        # Keep instructions, truncate schema
                        instructions_start = system_prompt.find("INSTRUCTIONS:")
                        instructions_part = system_prompt[instructions_start:]
                        schema_part = system_prompt[:instructions_start]

                        if len(instructions_part) <= system_target:
                            # Keep all instructions, truncate schema
                            available_schema = system_target - len(instructions_part)
                            final_system = schema_part[:available_schema] + instructions_part
                        else:
                            # Truncate instructions too
                            final_system = instructions_part[:system_target]
                    else:
                        final_system = system_prompt[:system_target]
                else:
                    final_system = system_prompt

                # Smart user prompt truncation
                if len(user_prompt) > user_target:
                    # Try to preserve section structure
                    sections = user_prompt.split('\n\n')
                    important_sections = []

                    for section in sections:
                        if any(keyword in section for keyword in ['Product details:', 'Enriched data:', 'Markdown content:']):
                            important_sections.append(section)

                    if important_sections:
                        # Distribute space among important sections
                        chars_per_section = user_target // len(important_sections)
                        truncated_sections = []

                        for section in important_sections:
                            if len(section) <= chars_per_section:
                                truncated_sections.append(section)
                            else:
                                # Truncate at word boundary
                                truncated = section[:chars_per_section]
                                last_space = truncated.rfind(' ')
                                if last_space > chars_per_section * 0.8:
                                    truncated = section[:last_space]
                                truncated_sections.append(truncated)

                        final_user = '\n\n'.join(truncated_sections)
                    else:
                        final_user = user_prompt[:user_target]
                else:
                    final_user = user_prompt

                print(f"  📝 Truncated lengths:")
                print(f"      System: {len(system_prompt):,} → {len(final_system):,} chars")
                print(f"      User: {len(user_prompt):,} → {len(final_user):,} chars")
                print(f"      Total: {len(final_system) + len(final_user):,} chars")
            else:
                final_system = system_prompt
                final_user = user_prompt
                print(f"  ✅ Using full prompts (within safe limits)")
                print(f"      System: {len(final_system):,} chars")
                print(f"      User: {len(final_user):,} chars")

            # Create model-specific prompt with proper image tokens
            combined_prompt = create_model_specific_prompt(
                model_name,  # Use the model name parameter
                final_system,
                final_user,
                len(processed_images)
            )

            # Monitor prompt length
            prompt_length = len(combined_prompt)
            print(f"  📏 Final prompt length: {prompt_length} chars")
            print(f"  🖼️ Images: {len(processed_images)} (each adds ~500-1000 tokens)")

            # Warn if prompt might be too long
            estimated_tokens = prompt_length // 4 + len(processed_images) * 750  # Rough estimate
            print(f"  🔢 Estimated tokens: ~{estimated_tokens} (max: 4096)")

            # Set up sampling parameters for very long responses
            stop_tokens = get_model_stop_tokens(model_name)
            sampling_params = SamplingParams(
                temperature=0.1,
                top_p=0.9,
                max_tokens=4096,  # MUCH HIGHER for long responses (up to 16k chars)
                repetition_penalty=1.05,  # Prevent repetition in long responses
                stop=stop_tokens
            )

            print(f"  🚀 Running inference...")
            start_time = time.time()

            # Run inference with single prompt
            try:
                outputs = llm.generate(
                    [{
                        "prompt": combined_prompt,
                        "multi_modal_data": {"image": processed_images}
                    }],
                    sampling_params=sampling_params
                )

                inference_time = time.time() - start_time

                if outputs and outputs[0].outputs:
                    generated_text = outputs[0].outputs[0].text.strip()

                    # Create detailed result
                    result = {
                        'prompt_id': f"prompt_{i}",
                        'system_prompt': truncated_system,
                        'user_prompt': truncated_user,
                        'num_images': len(processed_images),
                        'actual_response': actual_response,
                        'generated_response': generated_text,
                        'inference_time': inference_time,
                        'success': True,
                        'vllm_optimized': True,
                        'generation_stats': {
                            'output_tokens': len(outputs[0].outputs[0].token_ids) if hasattr(outputs[0].outputs[0], 'token_ids') else None,
                            'finish_reason': outputs[0].outputs[0].finish_reason if hasattr(outputs[0].outputs[0], 'finish_reason') else None,
                            'generated_length': len(generated_text),
                            'actual_length': len(actual_response)
                        }
                    }

                    # Add immediate comparison if actual response exists
                    if actual_response:
                        quick_similarity = calculate_text_similarity(generated_text, actual_response)
                        result['quick_similarity'] = quick_similarity

                    results.append(result)

                    print(f"  ✅ Completed in {inference_time:.2f}s")
                    print(f"  📝 Generated: {len(generated_text)} chars")
                    print(f"  📝 Actual: {len(actual_response)} chars")
                    if actual_response:
                        print(f"  🔍 Quick similarity: {result.get('quick_similarity', 0):.3f}")
                    print(f"  📄 Preview: {generated_text[:150]}...")

                else:
                    print(f"  ❌ No output generated for prompt {i}")

            except Exception as inference_error:
                print(f"  ❌ Inference error for prompt {i}: {str(inference_error)}")
                # Add failed result
                results.append({
                    'prompt_id': f"prompt_{i}",
                    'success': False,
                    'error': str(inference_error),
                    'vllm_optimized': True
                })
                continue

        except Exception as e:
            print(f"  ❌ Error processing prompt {i}: {str(e)}")
            results.append({
                'prompt_id': f"prompt_{i}",
                'success': False,
                'error': str(e),
                'vllm_optimized': True
            })
            continue

    successful_results = [r for r in results if r.get('success', False)]
    print(f"\n✅ Completed inference on {len(results)} prompts")
    print(f"📊 Successful: {len(successful_results)}, Failed: {len(results) - len(successful_results)}")

    return results

def calculate_text_similarity(text1: str, text2: str) -> float:
    """Calculate text similarity using difflib"""
    if not text1 or not text2:
        return 0.0

    # Normalize texts
    text1_clean = text1.lower().strip()
    text2_clean = text2.lower().strip()

    if text1_clean == text2_clean:
        return 1.0

    # Use difflib to calculate similarity
    matcher = difflib.SequenceMatcher(None, text1_clean, text2_clean)
    return matcher.ratio()

def extract_json_from_text(text: str) -> Dict:
    """Try to extract JSON from text response"""
    try:
        # Try direct JSON parsing
        return json.loads(text.strip())
    except json.JSONDecodeError:
        pass

    # Try to find JSON in text
    import re
    json_match = re.search(r'\{.*\}', text, re.DOTALL)
    if json_match:
        try:
            return json.loads(json_match.group(0))
        except json.JSONDecodeError:
            pass

    return {}

def compare_responses(generated: str, actual: str) -> Dict[str, Any]:
    """Compare generated response with actual response"""
    comparison = {
        'text_similarity': calculate_text_similarity(generated, actual),
        'generated_length': len(generated),
        'actual_length': len(actual),
        'length_ratio': len(generated) / len(actual) if actual else 0,
    }

    # Try JSON comparison
    try:
        generated_json = extract_json_from_text(generated)
        actual_json = extract_json_from_text(actual)

        if generated_json and actual_json:
            comparison['json_comparison'] = {
                'both_valid_json': True,
                'generated_keys': list(generated_json.keys()) if isinstance(generated_json, dict) else [],
                'actual_keys': list(actual_json.keys()) if isinstance(actual_json, dict) else [],
                'common_keys': list(set(generated_json.keys()) & set(actual_json.keys())) if isinstance(generated_json, dict) and isinstance(actual_json, dict) else [],
                'key_overlap_ratio': len(set(generated_json.keys()) & set(actual_json.keys())) / len(set(generated_json.keys()) | set(actual_json.keys())) if isinstance(generated_json, dict) and isinstance(actual_json, dict) and (generated_json.keys() or actual_json.keys()) else 0
            }
        else:
            comparison['json_comparison'] = {
                'both_valid_json': False,
                'generated_is_json': bool(generated_json),
                'actual_is_json': bool(actual_json)
            }
    except Exception as e:
        comparison['json_comparison'] = {'error': str(e)}

    return comparison

def analyze_and_save_results(results: List[Dict]):
    """Analyze results, compare with actual responses, and save to file"""
    print(f"\n📊 ANALYZING RESULTS AND COMPARING RESPONSES:")
    print("="*60)

    successful = [r for r in results if r.get('success', False)]

    if not results:
        print("❌ No results to analyze")
        return

    # Add comparisons to results
    comparisons = []
    for result in successful:
        generated = result.get('generated_response', '')
        actual = result.get('actual_response', '')

        if actual:  # Only compare if we have actual response
            comparison = compare_responses(generated, actual)
            result['comparison'] = comparison
            comparisons.append(comparison)

    # Calculate statistics
    total_time = sum(r.get('inference_time', 0) for r in successful)
    avg_time = total_time / len(successful) if successful else 0

    print(f"📈 INFERENCE SUMMARY:")
    print(f"  Total prompts: {len(results)}")
    print(f"  Successful: {len(successful)}")
    print(f"  Failed: {len(results) - len(successful)}")
    print(f"  Success rate: {len(successful)/len(results)*100:.1f}%")
    print(f"  Average time: {avg_time:.2f}s per prompt")
    print(f"  Total time: {total_time:.2f}s")

    # Comparison statistics
    if comparisons:
        avg_similarity = sum(c['text_similarity'] for c in comparisons) / len(comparisons)
        avg_length_ratio = sum(c['length_ratio'] for c in comparisons) / len(comparisons)
        json_success_rate = sum(1 for c in comparisons if c.get('json_comparison', {}).get('both_valid_json', False)) / len(comparisons)

        print(f"\n🔍 COMPARISON SUMMARY:")
        print(f"  Responses compared: {len(comparisons)}")
        print(f"  Average text similarity: {avg_similarity:.3f}")
        print(f"  Average length ratio: {avg_length_ratio:.3f}")
        print(f"  JSON success rate: {json_success_rate*100:.1f}%")

        if comparisons:
            key_overlaps = [c.get('json_comparison', {}).get('key_overlap_ratio', 0) for c in comparisons if c.get('json_comparison', {}).get('both_valid_json', False)]
            if key_overlaps:
                avg_key_overlap = sum(key_overlaps) / len(key_overlaps)
                print(f"  Average JSON key overlap: {avg_key_overlap:.3f}")

    # Save detailed results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f'vllm_vlm_results_{timestamp}.json'

    # Create comprehensive results structure
    comprehensive_results = {
        'metadata': {
            'timestamp': datetime.now().isoformat(),
            'total_prompts': len(results),
            'successful_prompts': len(successful),
            'failed_prompts': len(results) - len(successful),
            'success_rate': len(successful)/len(results) if results else 0,
            'total_inference_time': total_time,
            'average_inference_time': avg_time
        },
        'comparison_summary': {
            'responses_compared': len(comparisons),
            'average_text_similarity': sum(c['text_similarity'] for c in comparisons) / len(comparisons) if comparisons else 0,
            'average_length_ratio': sum(c['length_ratio'] for c in comparisons) / len(comparisons) if comparisons else 0,
            'json_success_rate': sum(1 for c in comparisons if c.get('json_comparison', {}).get('both_valid_json', False)) / len(comparisons) if comparisons else 0
        },
        'detailed_results': results
    }

    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(comprehensive_results, f, indent=2, ensure_ascii=False)

    print(f"\n💾 Comprehensive results saved to: {output_file}")

    # Save simple CSV for analysis
    csv_file = f'vllm_comparison_summary_{timestamp}.csv'
    try:
        import csv
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['prompt_id', 'num_images', 'inference_time', 'text_similarity', 'length_ratio', 'json_valid', 'key_overlap'])

            for result in successful:
                comparison = result.get('comparison', {})
                json_comp = comparison.get('json_comparison', {})
                writer.writerow([
                    result.get('prompt_id', ''),
                    result.get('num_images', 0),
                    result.get('inference_time', 0),
                    comparison.get('text_similarity', 0),
                    comparison.get('length_ratio', 0),
                    json_comp.get('both_valid_json', False),
                    json_comp.get('key_overlap_ratio', 0)
                ])

        print(f"📊 CSV summary saved to: {csv_file}")
    except ImportError:
        print("⚠️ CSV module not available, skipping CSV export")

    # Show detailed sample results
    print(f"\n📝 DETAILED SAMPLE RESULTS:")
    for i, result in enumerate(successful[:3], 1):
        print(f"\n{'='*20} Sample {i} ({'='*20}")
        print(f"Prompt ID: {result['prompt_id']}")
        print(f"Images: {result.get('num_images', 0)}")
        print(f"Inference time: {result.get('inference_time', 0):.2f}s")

        generated = result.get('generated_response', '')
        actual = result.get('actual_response', '')
        comparison = result.get('comparison', {})

        print(f"\nGenerated ({len(generated)} chars):")
        print(f"  {generated[:200]}...")

        print(f"\nActual ({len(actual)} chars):")
        print(f"  {actual[:200]}...")

        if comparison:
            print(f"\nComparison:")
            print(f"  Text similarity: {comparison.get('text_similarity', 0):.3f}")
            print(f"  Length ratio: {comparison.get('length_ratio', 0):.3f}")

            json_comp = comparison.get('json_comparison', {})
            if json_comp.get('both_valid_json', False):
                print(f"  JSON key overlap: {json_comp.get('key_overlap_ratio', 0):.3f}")
                print(f"  Common keys: {len(json_comp.get('common_keys', []))}")
            else:
                print(f"  JSON status: Generated={json_comp.get('generated_is_json', False)}, Actual={json_comp.get('actual_is_json', False)}")

    return comprehensive_results

def main():
    """Main execution function"""
    print("🚀 vLLM VLM Inference with Response Comparison")
    print("="*60)

    # Configuration - Using ultra-high-context model
    MODEL_NAME = "phi3-vision"  # 128k context, perfect for very long prompts
    MAX_PROMPTS = 10  # Process all prompts
    MAX_IMAGES_PER_PROMPT = 2  # Limit images for memory efficiency
    MAX_TOKENS = 4096  # INCREASED for very long responses (up to 16k chars)
    TEMPERATURE = 0.1  # Low temperature for consistent results

    print(f"⚙️ Configuration:")
    print(f"  Model: {MODEL_NAME}")
    print(f"  Max prompts: {MAX_PROMPTS}")
    print(f"  Max images per prompt: {MAX_IMAGES_PER_PROMPT}")
    print(f"  Max output tokens: {MAX_TOKENS}")
    print(f"  Temperature: {TEMPERATURE}")

    # Step 1: Load prompts
    print(f"\n📂 STEP 1: LOADING PROMPTS")
    print("-" * 40)

    # Try both possible input files (JSONL format)
    input_files = ['final_image_prompts_cleaned.jsonl', 'final_image_prompts.jsonl']
    prompts = None

    for input_file in input_files:
        prompts = load_prompts_from_jsonl(input_file)
        if prompts:
            print(f"✅ Loaded prompts from: {input_file}")
            break

    if not prompts:
        print("❌ No prompts loaded from any file, exiting")
        print("Expected files: final_image_prompts_cleaned.jsonl or final_image_prompts.jsonl")
        return

    print(f"📊 Found {len(prompts)} prompts total")

    # Step 2: Initialize vLLM model
    print(f"\n🤖 STEP 2: INITIALIZING vLLM MODEL")
    print("-" * 40)

    try:
        llm = initialize_vllm_model(MODEL_NAME, MAX_IMAGES_PER_PROMPT)
    except Exception as e:
        print(f"❌ Failed to initialize model: {str(e)}")
        print("\n🔧 Troubleshooting:")
        print("1. Install vLLM: pip install vllm")
        print("2. Check model availability")
        print("3. Verify GPU memory")
        return

    # Step 3: Run inference with comparison
    print(f"\n🔥 STEP 3: RUNNING vLLM INFERENCE WITH COMPARISON")
    print("-" * 40)

    # Update sampling parameters
    global sampling_params
    sampling_params = SamplingParams(
        temperature=TEMPERATURE,
        top_p=0.9,
        max_tokens=MAX_TOKENS,
        stop=["</s>", "<|endoftext|>", "<|im_end|>"]
    )

    results = run_vllm_inference(llm, prompts, MAX_PROMPTS, MODEL_NAME)

    if not results:
        print("❌ No results generated, exiting")
        return

    # Step 4: Analyze, compare, and save results
    print(f"\n📊 STEP 4: ANALYZING AND COMPARING RESULTS")
    print("-" * 40)

    comprehensive_results = analyze_and_save_results(results)

    # Step 5: Summary and recommendations
    print(f"\n🎯 STEP 5: SUMMARY AND RECOMMENDATIONS")
    print("-" * 40)

    if comprehensive_results:
        metadata = comprehensive_results.get('metadata', {})
        comparison_summary = comprehensive_results.get('comparison_summary', {})

        print(f"🏆 FINAL PERFORMANCE METRICS:")
        print(f"  Success Rate: {metadata.get('success_rate', 0)*100:.1f}%")
        print(f"  Average Inference Time: {metadata.get('average_inference_time', 0):.2f}s")
        print(f"  Average Text Similarity: {comparison_summary.get('average_text_similarity', 0):.3f}")
        print(f"  JSON Success Rate: {comparison_summary.get('json_success_rate', 0)*100:.1f}%")

        # Recommendations based on results
        avg_similarity = comparison_summary.get('average_text_similarity', 0)
        json_success = comparison_summary.get('json_success_rate', 0)

        print(f"\n💡 RECOMMENDATIONS:")
        if avg_similarity < 0.3:
            print(f"  ⚠️ Low text similarity ({avg_similarity:.3f}) - Consider prompt engineering")
        elif avg_similarity > 0.7:
            print(f"  ✅ Good text similarity ({avg_similarity:.3f}) - Model performing well")

        if json_success < 0.5:
            print(f"  ⚠️ Low JSON success rate ({json_success*100:.1f}%) - Improve output formatting")
        elif json_success > 0.8:
            print(f"  ✅ High JSON success rate ({json_success*100:.1f}%) - Structured output working well")

    print(f"\n🎉 vLLM VLM INFERENCE WITH COMPARISON COMPLETED!")
    print(f"💡 Benefits achieved:")
    print(f"  ✅ Paged attention for memory efficiency")
    print(f"  ✅ Batch processing for speed")
    print(f"  ✅ Automated response comparison")
    print(f"  ✅ Detailed performance metrics")
    print(f"  ✅ Comprehensive result storage")

    print(f"\n📁 Output files generated:")
    print(f"  - vllm_vlm_results_[timestamp].json (detailed results)")
    print(f"  - vllm_comparison_summary_[timestamp].csv (analysis summary)")

    return comprehensive_results

if __name__ == "__main__":
    main()
