#!/usr/bin/env python3
"""
Gemma-3n Vision-Language Model Inference
Clean architecture with utils, URL removal, temperature control
"""

import json
import time
import torch
from datetime import datetime

# Import utilities from utils folder
from utils.data_loader import load_prompts_from_jsonl
from utils.text_processor import remove_urls_from_text, estimate_tokens
from utils.gemma_model_initializer import initialize_gemma3n, create_gemma_messages
from utils.gemma_image_processor import prepare_image_for_gemma

def main():
    """Clean Gemma-3n inference with URL removal"""
    print("🔮 Gemma-3n Vision-Language Model Inference")
    print("=" * 60)
    print("🎯 Features: URL removal, No truncation, Clean architecture")
    
    # Load prompts
    input_files = ['final_image_prompts_cleaned.jsonl', 'scrape_content_prompts.jsonl']
    prompts = None
    
    for input_file in input_files:
        prompts = load_prompts_from_jsonl(input_file)
        if prompts:
            print(f"✅ Using prompts from: {input_file}")
            break
    
    if not prompts:
        print("❌ No prompts found")
        return
    
    # Initialize Gemma-3n model
    print(f"\n🤖 INITIALIZING GEMMA-3N MODEL")
    print("-" * 50)
    
    try:
        model, processor = initialize_gemma3n()
        print(f"✅ Gemma-3n ready for inference")
    except Exception as e:
        print(f"❌ Model initialization failed: {e}")
        return
    
    # Process prompts
    results = []
    max_prompts = min(len(prompts), 5)  # Process up to 5 prompts
    
    print(f"\n🚀 PROCESSING {max_prompts} PROMPTS")
    print("=" * 60)
    
    for i, prompt_data in enumerate(prompts[:max_prompts], 1):
        print(f"\n{'='*15} PROMPT {i}/{max_prompts} {'='*15}")
        
        try:
            # Extract data
            system_prompt = prompt_data.get('SystemPrompt', '')
            user_prompt = prompt_data.get('UserPrompt', '')
            image_data_urls = prompt_data.get('Images', [])
            
            print(f"📋 Original lengths:")
            print(f"  System: {len(system_prompt):,} chars")
            print(f"  User: {len(user_prompt):,} chars")
            print(f"  Images: {len(image_data_urls)}")
            
            # Remove URLs to reduce token count
            print("🔗 Removing URLs...")
            cleaned_system, system_urls = remove_urls_from_text(system_prompt)
            cleaned_user, user_urls = remove_urls_from_text(user_prompt)
            
            total_urls = system_urls + user_urls
            total_saved = (len(system_prompt) - len(cleaned_system)) + (len(user_prompt) - len(cleaned_user))
            
            print(f"📝 URL removal results:")
            print(f"  URLs removed: {total_urls}")
            print(f"  Characters saved: {total_saved:,}")
            print(f"  Final system: {len(cleaned_system):,} chars")
            print(f"  Final user: {len(cleaned_user):,} chars")
            
            # Process images (Gemma-3n can handle multiple images)
            processed_images = []
            print(f"🖼️ Processing images for Gemma-3n...")
            
            for j, img_data in enumerate(image_data_urls[:3]):  # Limit to 3 images
                image = prepare_image_for_gemma(img_data)
                if image:
                    processed_images.append(image)
                    print(f"  ✓ Image {j+1}: {image.size}")
                else:
                    print(f"  ✗ Image {j+1}: failed")
            
            if not processed_images:
                print("❌ No valid images, skipping prompt")
                continue
            
            # Estimate tokens (for information)
            combined_text = cleaned_system + cleaned_user
            token_estimate = estimate_tokens(combined_text, len(processed_images))
            
            print(f"📊 Token estimation (approximate):")
            print(f"  Text: {token_estimate['text_tokens']:,} tokens")
            print(f"  Images: {token_estimate['image_tokens']:,} tokens")
            print(f"  Total: {token_estimate['total_tokens']:,} tokens")
            
            # Create Gemma-3n messages format
            messages = create_gemma_messages(cleaned_system, cleaned_user, processed_images)
            
            print(f"📏 Created Gemma-3n message format")
            print(f"📝 System prompt: {len(cleaned_system):,} chars")
            print(f"📝 User prompt: {len(cleaned_user):,} chars")
            print(f"🖼️ Images: {len(processed_images)}")
            
            # Prepare inputs for Gemma-3n
            print("🔄 Preparing inputs...")
            inputs = processor.apply_chat_template(
                messages,
                add_generation_prompt=True,
                tokenize=True,
                return_dict=True,
                return_tensors="pt",
            ).to(model.device, dtype=torch.bfloat16)
            
            input_len = inputs["input_ids"].shape[-1]
            print(f"📊 Actual input tokens: {input_len:,}")
            
            # Generation parameters
            max_new_tokens = 2000  # Reasonable output length
            temperature = 0.3  # Balanced creativity/consistency
            
            print(f"🎛️ Generation settings:")
            print(f"  Max new tokens: {max_new_tokens:,}")
            print(f"  Temperature: {temperature}")
            
            # Run inference
            print("🚀 Running Gemma-3n inference...")
            start_time = time.time()
            
            with torch.inference_mode():
                generation = model.generate(
                    **inputs,
                    max_new_tokens=max_new_tokens,
                    temperature=temperature,
                    do_sample=True if temperature > 0 else False,
                    top_p=0.9,
                    repetition_penalty=1.05,
                    pad_token_id=processor.tokenizer.eos_token_id
                )
                
                # Extract only the generated part
                generation = generation[0][input_len:]
            
            inference_time = time.time() - start_time
            
            # Decode the response
            generated_text = processor.decode(generation, skip_special_tokens=True)
            
            print(f"✅ Inference completed in {inference_time:.2f}s")
            print(f"📝 Generated: {len(generated_text):,} chars")
            print(f"📄 Preview: {generated_text[:300]}...")
            
            # Store results
            result = {
                'prompt_id': f"prompt_{i}",
                'model': "google/gemma-3n-e2b-it",
                'original_system_length': len(system_prompt),
                'original_user_length': len(user_prompt),
                'cleaned_system_length': len(cleaned_system),
                'cleaned_user_length': len(cleaned_user),
                'urls_removed': total_urls,
                'chars_saved': total_saved,
                'num_images': len(processed_images),
                'input_tokens': input_len,
                'generated_response': generated_text,
                'generated_length': len(generated_text),
                'inference_time': inference_time,
                'temperature': temperature,
                'max_new_tokens': max_new_tokens,
                'success': True
            }
            
            results.append(result)
            
        except Exception as e:
            print(f"❌ Error processing prompt {i}: {e}")
            results.append({
                'prompt_id': f"prompt_{i}",
                'success': False,
                'error': str(e)
            })
    
    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f'gemma3n_results_{timestamp}.json'
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    # Summary
    successful = [r for r in results if r.get('success', False)]
    
    print(f"\n📊 SUMMARY:")
    print("=" * 60)
    print(f"🤖 Model: Gemma-3n-e2b-it")
    print(f"📊 Processed: {len(results)} prompts")
    print(f"✅ Successful: {len(successful)}")
    print(f"🌡️ Temperature: 0.3")
    
    if successful:
        avg_time = sum(r['inference_time'] for r in successful) / len(successful)
        total_urls = sum(r['urls_removed'] for r in successful)
        total_saved = sum(r['chars_saved'] for r in successful)
        avg_length = sum(r['generated_length'] for r in successful) / len(successful)
        avg_input_tokens = sum(r['input_tokens'] for r in successful) / len(successful)
        
        print(f"⏱️ Average time: {avg_time:.2f}s")
        print(f"🔗 Total URLs removed: {total_urls:,}")
        print(f"💾 Total chars saved: {total_saved:,}")
        print(f"📝 Average response: {avg_length:,.0f} chars")
        print(f"📊 Average input tokens: {avg_input_tokens:,.0f}")
    
    print(f"\n💾 Results saved to: {results_file}")
    print("🎉 Gemma-3n inference completed!")

if __name__ == "__main__":
    main()
