#!/usr/bin/env python3
"""
Simple test script for Gemma-3n model
Tests the basic functionality with a sample image
"""

import torch
from utils.gemma_model_initializer import initialize_gemma3n, create_gemma_messages
from utils.gemma_image_processor import download_image_from_url

def test_gemma3n():
    """Test Gemma-3n with a simple example"""
    print("🧪 Testing Gemma-3n Model")
    print("=" * 40)
    
    # Initialize model
    try:
        model, processor = initialize_gemma3n()
        print("✅ Model initialized successfully")
    except Exception as e:
        print(f"❌ Model initialization failed: {e}")
        return
    
    # Download test image
    print("\n📥 Downloading test image...")
    test_image_url = "https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/bee.jpg"
    test_image = download_image_from_url(test_image_url)
    
    if test_image is None:
        print("❌ Failed to download test image")
        return
    
    print(f"✅ Test image loaded: {test_image.size}")
    
    # Create test messages
    system_prompt = "You are a helpful assistant that analyzes images in detail."
    user_prompt = "Describe this image in detail, including colors, objects, and overall composition."
    
    messages = create_gemma_messages(system_prompt, user_prompt, [test_image])
    
    print("\n🔄 Preparing inputs...")
    inputs = processor.apply_chat_template(
        messages,
        add_generation_prompt=True,
        tokenize=True,
        return_dict=True,
        return_tensors="pt",
    ).to(model.device, dtype=torch.bfloat16)
    
    input_len = inputs["input_ids"].shape[-1]
    print(f"📊 Input tokens: {input_len:,}")
    
    # Run inference
    print("🚀 Running test inference...")
    
    with torch.inference_mode():
        generation = model.generate(
            **inputs,
            max_new_tokens=200,
            temperature=0.3,
            do_sample=True,
            top_p=0.9,
            pad_token_id=processor.tokenizer.eos_token_id
        )
        
        # Extract generated part
        generation = generation[0][input_len:]
    
    # Decode response
    response = processor.decode(generation, skip_special_tokens=True)
    
    print("✅ Test completed!")
    print("\n📝 Generated Response:")
    print("-" * 40)
    print(response)
    print("-" * 40)
    
    print(f"\n📊 Response length: {len(response)} characters")
    print("🎉 Gemma-3n test successful!")

if __name__ == "__main__":
    test_gemma3n()
