#!/usr/bin/env python3
"""
Complete Gemma-3n Vision-Language Model Inference
Single file with authentication, error handling, and inference
"""

import json
import time
import torch
import os
import base64
import requests
import warnings
from datetime import datetime
from io import BytesIO
from PIL import Image
from transformers import AutoProcessor, Gemma3nForConditionalGeneration
from huggingface_hub import login, HfApi

# Suppress warnings and handle version compatibility
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=FutureWarning)

# Monkey patch for torch.compiler compatibility
if not hasattr(torch.compiler, 'is_compiling'):
    torch.compiler.is_compiling = lambda: False

def check_huggingface_auth():
    """Check if user is authenticated with Hugging Face"""
    try:
        api = HfApi()
        user_info = api.whoami()
        print(f"✅ Authenticated as: {user_info['name']}")
        return True
    except Exception:
        return False

def huggingface_login():
    """Handle Hugging Face authentication"""
    print("🔐 Hugging Face Authentication Required")

    # Check if already authenticated
    if check_huggingface_auth():
        print("✅ Already authenticated with Hugging Face")
        return True

    # Check for token in environment
    hf_token = os.getenv('HF_TOKEN') or os.getenv('HUGGINGFACE_HUB_TOKEN')

    if hf_token:
        print("🔑 Found HF token in environment variables")
        try:
            login(token=hf_token)
            if check_huggingface_auth():
                print("✅ Successfully authenticated with environment token")
                return True
        except Exception as e:
            print(f"❌ Environment token failed: {e}")

    # Interactive login
    print("\n📝 Interactive login required:")
    print("1. Go to https://huggingface.co/settings/tokens")
    print("2. Create a new token with 'Read' permissions")
    print("3. Copy the token and paste it when prompted")

    try:
        login()
        if check_huggingface_auth():
            print("✅ Successfully authenticated interactively")
            return True
    except Exception as e:
        print(f"❌ Interactive login failed: {e}")

    return False

def load_prompts_from_jsonl(file_path: str):
    """Load prompts from JSONL file"""
    print(f"📂 Loading prompts from {file_path}...")

    try:
        prompts = []
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Handle formatted JSON records
        brace_count = 0
        current_record = ""

        for line in content.split('\n'):
            if line.strip() == '{' and brace_count == 0:
                if current_record.strip():
                    try:
                        record = json.loads(current_record.strip())
                        prompts.append(record)
                    except json.JSONDecodeError:
                        pass
                current_record = line + '\n'
                brace_count = 1
            elif brace_count > 0:
                current_record += line + '\n'
                brace_count += line.count('{') - line.count('}')

                if brace_count == 0:
                    try:
                        record = json.loads(current_record.strip())
                        prompts.append(record)
                    except json.JSONDecodeError:
                        pass
                    current_record = ""

        if current_record.strip():
            try:
                record = json.loads(current_record.strip())
                prompts.append(record)
            except json.JSONDecodeError:
                pass

        print(f"✅ Loaded {len(prompts)} prompts")
        return prompts

    except Exception as e:
        print(f"❌ Error loading prompts: {str(e)}")
        return []

def prepare_image_from_base64(base64_data: str):
    """Convert base64 to PIL Image with compatibility fixes"""
    try:
        if base64_data.startswith('data:'):
            header, base64_content = base64_data.split(',', 1)
        else:
            base64_content = base64_data.strip()

        image_bytes = base64.b64decode(base64_content)
        image = Image.open(BytesIO(image_bytes))

        if image.mode != 'RGB':
            image = image.convert('RGB')

        # Very conservative sizing to avoid transformers/torch compatibility issues
        max_size = 384  # Even smaller to avoid processing errors
        if max(image.size) > max_size:
            original_size = image.size
            # Use LANCZOS if available, otherwise BICUBIC
            try:
                resample = Image.Resampling.LANCZOS
            except AttributeError:
                resample = Image.LANCZOS

            image.thumbnail((max_size, max_size), resample)
            print(f"    📏 Resized: {original_size} → {image.size}")

        # Ensure image is properly formatted
        if image.size[0] == 0 or image.size[1] == 0:
            raise ValueError("Invalid image dimensions")

        return image
    except Exception as e:
        print(f"    ❌ Image error: {e}")
        return None

def remove_urls_from_text(text: str):
    """Remove URLs from text to reduce token count"""
    import re

    url_patterns = [
        r'https?://[^\s<>"]+',
        r'www\.[^\s<>"]+',
        r'ftp://[^\s<>"]+',
        r'[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(?:/[^\s<>"]*)?',
    ]

    cleaned_text = text
    urls_removed = 0

    for pattern in url_patterns:
        matches = re.findall(pattern, cleaned_text)
        urls_removed += len(matches)
        cleaned_text = re.sub(pattern, '', cleaned_text)

    # Clean up whitespace
    cleaned_text = re.sub(r'\s+', ' ', cleaned_text)
    cleaned_text = re.sub(r'\n\s*\n\s*\n', '\n\n', cleaned_text)
    cleaned_text = cleaned_text.strip()

    return cleaned_text, urls_removed

def download_test_image():
    """Download a test image for demonstration"""
    try:
        url = "https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/bee.jpg"
        response = requests.get(url)
        image = Image.open(BytesIO(response.content))
        if image.mode != 'RGB':
            image = image.convert('RGB')
        return image
    except Exception as e:
        print(f"❌ Error downloading test image: {e}")
        return None

def main():
    """Complete Gemma-3n inference with authentication and error handling"""
    print("🔮 Complete Gemma-3n Vision-Language Model Inference")
    print("=" * 60)

    # Step 1: Authentication
    print("\n🔐 STEP 1: Authentication")
    print("-" * 30)
    if not huggingface_login():
        print("❌ Authentication failed. Please set up HF_TOKEN or login interactively.")
        return

    # Step 2: Initialize model
    print("\n🤖 STEP 2: Model Initialization")
    print("-" * 30)

    model_id = "google/gemma-3n-e2b-it"

    try:
        print(f"📊 Loading {model_id}...")

        # Set environment variables for compatibility
        os.environ["TOKENIZERS_PARALLELISM"] = "false"

        # Load processor with error handling
        print("🔄 Loading processor...")
        processor = AutoProcessor.from_pretrained(
            model_id,
            trust_remote_code=True
        )
        print("✅ Processor loaded successfully")

        # Load model with very conservative settings for compatibility
        print("🔄 Loading model...")
        model = Gemma3nForConditionalGeneration.from_pretrained(
            model_id,
            device_map="auto",
            torch_dtype=torch.bfloat16,
            trust_remote_code=True,
            low_cpu_mem_usage=True,
            use_safetensors=True,
            attn_implementation="eager"  # Use eager attention to avoid compilation issues
        ).eval()

        print("✅ Model loaded successfully")
        print(f"💾 Model device: {next(model.parameters()).device}")
        print(f"🔢 Model dtype: {next(model.parameters()).dtype}")

    except Exception as e:
        print(f"❌ Model loading failed: {e}")

        # Provide specific error handling
        if "gated" in str(e) or "401" in str(e):
            print("\n🚨 Model access required:")
            print("1. Visit: https://huggingface.co/google/gemma-3n-e2b-it")
            print("2. Request access to the model")
            print("3. Wait for approval")
        elif "compiler" in str(e) or "is_compiling" in str(e):
            print("\n🚨 PyTorch version compatibility issue:")
            print("Try upgrading PyTorch: pip install torch --upgrade")
        elif "CUDA" in str(e):
            print("\n🚨 CUDA memory issue:")
            print("Try reducing batch size or using CPU")

        return

    # Step 3: Test with simple example
    print("\n🧪 STEP 3: Simple Test")
    print("-" * 30)

    # Download test image
    test_image = download_test_image()
    if test_image:
        print(f"✅ Test image loaded: {test_image.size}")

        # Create simple test messages
        messages = [
            {
                "role": "user",
                "content": [
                    {"type": "image", "image": test_image},
                    {"type": "text", "text": "Describe this image briefly."}
                ]
            }
        ]

        try:
            # Process with extensive error handling
            print("🔄 Processing test input...")

            # Disable torch compilation to avoid compatibility issues
            with torch.no_grad():
                # Set environment for stability
                torch.backends.cudnn.deterministic = True

                # Process inputs with error handling
                try:
                    inputs = processor.apply_chat_template(
                        messages,
                        add_generation_prompt=True,
                        tokenize=True,
                        return_dict=True,
                        return_tensors="pt"
                    )
                except Exception as proc_error:
                    print(f"❌ Processor error: {proc_error}")
                    # Try alternative approach
                    print("🔄 Trying alternative processing...")

                    # Manual text formatting as fallback
                    text_content = "Describe this image briefly."
                    inputs = processor.tokenizer(
                        text_content,
                        return_tensors="pt",
                        padding=True,
                        truncation=True
                    )
                    # Note: This fallback won't include image, but tests tokenizer

                # Move to model device safely
                device = next(model.parameters()).device
                inputs = {k: v.to(device) if isinstance(v, torch.Tensor) else v for k, v in inputs.items()}

                input_len = inputs["input_ids"].shape[-1]
                print(f"📊 Input tokens: {input_len:,}")

                # Generate response with conservative settings
                print("🚀 Generating response...")

                generation = model.generate(
                    **inputs,
                    max_new_tokens=100,  # Reduced for stability
                    temperature=0.1,     # Lower temperature for stability
                    do_sample=False,     # Greedy decoding for stability
                    pad_token_id=processor.tokenizer.eos_token_id,
                    eos_token_id=processor.tokenizer.eos_token_id,
                    use_cache=True
                )

                # Extract generated part
                generation = generation[0][input_len:]

            # Decode response
            response = processor.decode(generation, skip_special_tokens=True)

            print("✅ Test successful!")
            print(f"\n📝 Generated Response:")
            print("-" * 40)
            print(response)
            print("-" * 40)

        except Exception as e:
            print(f"❌ Test inference failed: {e}")
            print("\n🔧 Troubleshooting suggestions:")
            print("1. Try upgrading transformers: pip install transformers --upgrade")
            print("2. Try upgrading torch: pip install torch --upgrade")
            print("3. The model may work for text-only inputs")

            # Continue anyway - the model might work for actual prompts
            print("\n🔄 Continuing to actual prompts (model is loaded)...")

    # Step 4: Process actual prompts if available
    print("\n📋 STEP 4: Process Actual Prompts")
    print("-" * 30)

    # Try to load prompts
    input_files = ['final_image_prompts_cleaned.jsonl', 'scrape_content_prompts.jsonl']
    prompts = None

    for input_file in input_files:
        if os.path.exists(input_file):
            prompts = load_prompts_from_jsonl(input_file)
            if prompts:
                print(f"✅ Using prompts from: {input_file}")
                break

    if not prompts:
        print("ℹ️ No prompt files found, test completed successfully")
        return

    # Process a few prompts
    results = []
    max_prompts = min(len(prompts), 3)

    for i, prompt_data in enumerate(prompts[:max_prompts], 1):
        print(f"\n{'='*15} PROMPT {i}/{max_prompts} {'='*15}")

        try:
            # Extract and clean data
            system_prompt = prompt_data.get('SystemPrompt', '')
            user_prompt = prompt_data.get('UserPrompt', '')
            image_data_urls = prompt_data.get('Images', [])

            # Remove URLs
            cleaned_system, system_urls = remove_urls_from_text(system_prompt)
            cleaned_user, user_urls = remove_urls_from_text(user_prompt)

            print(f"🔗 URLs removed: {system_urls + user_urls}")

            # Process images
            processed_images = []
            for j, img_data in enumerate(image_data_urls[:2]):  # Limit to 2 images
                image = prepare_image_from_base64(img_data)
                if image:
                    processed_images.append(image)
                    print(f"  ✓ Image {j+1}: {image.size}")

            if not processed_images:
                print("❌ No valid images, skipping")
                continue

            # Create messages
            user_content = []
            for img in processed_images:
                user_content.append({"type": "image", "image": img})
            user_content.append({"type": "text", "text": f"{cleaned_system}\n\n{cleaned_user}"})

            messages = [{"role": "user", "content": user_content}]

            # Process and generate
            inputs = processor.apply_chat_template(
                messages,
                add_generation_prompt=True,
                tokenize=True,
                return_dict=True,
                return_tensors="pt"
            )

            device = next(model.parameters()).device
            inputs = {k: v.to(device) if isinstance(v, torch.Tensor) else v for k, v in inputs.items()}

            input_len = inputs["input_ids"].shape[-1]

            start_time = time.time()
            with torch.inference_mode():
                generation = model.generate(
                    **inputs,
                    max_new_tokens=1000,
                    temperature=0.3,
                    do_sample=True,
                    top_p=0.9,
                    pad_token_id=processor.tokenizer.eos_token_id
                )
                generation = generation[0][input_len:]

            inference_time = time.time() - start_time
            response = processor.decode(generation, skip_special_tokens=True)

            print(f"✅ Success in {inference_time:.2f}s")
            print(f"📝 Generated: {len(response):,} chars")

            results.append({
                'prompt_id': f"prompt_{i}",
                'generated_response': response,
                'inference_time': inference_time,
                'success': True
            })

        except Exception as e:
            print(f"❌ Error: {e}")
            results.append({
                'prompt_id': f"prompt_{i}",
                'success': False,
                'error': str(e)
            })

    # Save results
    if results:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f'gemma3n_complete_results_{timestamp}.json'

        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)

        successful = [r for r in results if r.get('success', False)]
        print(f"\n📊 SUMMARY: {len(successful)}/{len(results)} successful")
        print(f"💾 Results saved to: {results_file}")

    print("\n🎉 Gemma-3n inference completed!")

if __name__ == "__main__":
    main()
