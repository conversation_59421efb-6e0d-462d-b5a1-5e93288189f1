# vLLM VLM Inference Requirements
# Core dependencies for running vLLM with Vision-Language Models

# Core vLLM package
vllm>=0.3.0

# PyTorch with CUDA support (adjust CUDA version as needed)
torch>=2.1.0
torchvision>=0.16.0
torchaudio>=2.1.0

# Transformers and related packages
transformers>=4.36.0
tokenizers>=0.15.0
accelerate>=0.25.0

# Image processing
Pillow>=9.5.0
opencv-python>=4.8.0

# Core ML dependencies
numpy>=1.24.0
scipy>=1.10.0

# Text processing
sentencepiece>=0.1.99
protobuf>=4.21.0

# HTTP and async support
aiohttp>=3.8.0
fastapi>=0.104.0
uvicorn>=0.24.0

# Memory optimization (optional but recommended)
xformers>=0.0.22

# Flash Attention (optional, for better performance)
# flash-attn>=2.3.0  # Uncomment if you can install it

# Utility packages
tqdm>=4.65.0
psutil>=5.9.0
ray>=2.8.0

# JSON and data handling
pydantic>=2.0.0
typing-extensions>=4.5.0

# Logging and monitoring
wandb>=0.16.0  # Optional, for experiment tracking

# Development and testing (optional)
pytest>=7.4.0
black>=23.0.0
isort>=5.12.0

# Additional dependencies for specific models
# Uncomment as needed based on which models you use:

# For Qwen models
# tiktoken>=0.5.0

# For LLaVA models  
# einops>=0.7.0

# For Phi-3 Vision
# flash-attn>=2.3.0  # If available

# For InternVL models
# timm>=0.9.0
