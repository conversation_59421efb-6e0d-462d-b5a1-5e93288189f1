#!/usr/bin/env python3
"""
InternVL3-8B Shape-Safe Inference
Avoids dynamic preprocessing to prevent tensor shape errors
"""

import json
import time
import torch
import gc
import os
import base64
from datetime import datetime
from io import BytesIO
from PIL import Image
import torchvision.transforms as T

# Set memory optimization environment variables
os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "expandable_segments:True"

# Import utilities
from utils.data_loader import load_prompts_from_jsonl
from utils.text_processor import remove_urls_from_text

def initialize_internvl3_safe():
    """Initialize InternVL3 with safe settings"""
    from transformers import AutoTokenizer, AutoModel
    
    model_id = "OpenGVLab/InternVL3-8B"
    
    print(f"🤖 Loading InternVL3 with shape-safe settings...")
    
    # Load tokenizer
    tokenizer = AutoTokenizer.from_pretrained(model_id, trust_remote_code=True, use_fast=False)
    
    # Load model with safe settings
    model = AutoModel.from_pretrained(
        model_id,
        torch_dtype=torch.bfloat16,
        load_in_8bit=True,
        low_cpu_mem_usage=True,
        use_flash_attn=False,  # Disabled for stability
        trust_remote_code=True,
        device_map="auto"
    ).eval()
    
    return model, tokenizer

def process_image_simple(base64_data: str, size=448):
    """Simple image processing without dynamic preprocessing"""
    try:
        # Decode image
        if base64_data.startswith('data:'):
            header, base64_content = base64_data.split(',', 1)
        else:
            base64_content = base64_data.strip()
        
        image_bytes = base64.b64decode(base64_content)
        image = Image.open(BytesIO(image_bytes)).convert('RGB')
        
        print(f"    📏 Original size: {image.size}")
        
        # Simple resize to fixed size - no dynamic preprocessing
        image = image.resize((size, size), Image.Resampling.LANCZOS)
        print(f"    📏 Resized to: {image.size}")
        
        # Standard ImageNet normalization
        transform = T.Compose([
            T.ToTensor(),
            T.Normalize(mean=(0.485, 0.456, 0.406), std=(0.229, 0.224, 0.225))
        ])
        
        # Process as single tensor
        pixel_values = transform(image).unsqueeze(0)  # Shape: [1, 3, size, size]
        
        print(f"    📏 Final tensor: {pixel_values.shape}")
        
        # Validate shape
        expected_shape = (1, 3, size, size)
        if pixel_values.shape != expected_shape:
            print(f"    ❌ Shape mismatch: got {pixel_values.shape}, expected {expected_shape}")
            return None
        
        return pixel_values
        
    except Exception as e:
        print(f"    ❌ Simple processing error: {e}")
        return None

def main():
    """Shape-safe InternVL3 inference"""
    print("🔒 InternVL3-8B Shape-Safe Inference")
    print("=" * 60)
    print("🎯 No dynamic preprocessing - fixed tensor shapes")
    
    # Clear any existing GPU memory
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        gc.collect()
    
    # Load prompts
    input_files = ['final_image_prompts_cleaned.jsonl', 'scrape_content_prompts.jsonl']
    prompts = None
    
    for input_file in input_files:
        prompts = load_prompts_from_jsonl(input_file)
        if prompts:
            print(f"✅ Using prompts from: {input_file}")
            break
    
    if not prompts:
        print("❌ No prompts found")
        return
    
    # Initialize model
    try:
        model, tokenizer = initialize_internvl3_safe()
        print("✅ Model loaded with shape-safe settings")
    except Exception as e:
        print(f"❌ Model loading failed: {e}")
        return
    
    # Process prompts with shape safety
    results = []
    max_prompts = min(len(prompts), 3)  # Conservative limit
    
    print(f"\n🚀 PROCESSING {max_prompts} PROMPTS (SHAPE-SAFE)")
    print("=" * 60)
    
    for i, prompt_data in enumerate(prompts[:max_prompts], 1):
        print(f"\n{'='*15} PROMPT {i}/{max_prompts} {'='*15}")
        
        # Clear memory before each prompt
        torch.cuda.empty_cache()
        gc.collect()
        
        try:
            # Extract and clean data
            system_prompt = prompt_data.get('SystemPrompt', '')
            user_prompt = prompt_data.get('UserPrompt', '')
            image_data_urls = prompt_data.get('Images', [])
            
            # Remove URLs
            cleaned_system, system_urls = remove_urls_from_text(system_prompt)
            cleaned_user, user_urls = remove_urls_from_text(user_prompt)
            
            print(f"🔗 URLs removed: {system_urls + user_urls}")
            
            # Process only first image with simple method
            if not image_data_urls:
                print("❌ No images, skipping")
                continue
            
            print("🖼️ Processing first image (simple method)...")
            pixel_values = process_image_simple(image_data_urls[0], size=448)
            
            if pixel_values is None:
                print("❌ Image processing failed, trying smaller size...")
                pixel_values = process_image_simple(image_data_urls[0], size=224)
                
                if pixel_values is None:
                    print("❌ All image processing failed")
                    continue
            
            # Move to GPU with validation
            device = next(model.parameters()).device
            
            print(f"📊 Tensor before GPU: {pixel_values.shape}")
            pixel_values = pixel_values.to(torch.bfloat16).to(device)
            print(f"📊 Tensor on GPU: {pixel_values.shape}")
            
            # Create simple prompt
            question = f"<image>\n{cleaned_system[:1000]}\n\n{cleaned_user[:1000]}"
            print(f"📝 Question length: {len(question)} chars")
            
            # Simple generation config
            generation_config = {
                'max_new_tokens': 1000,
                'temperature': 0.3,
                'do_sample': True,
                'top_p': 0.9
            }
            
            # Run inference with shape safety
            print("🚀 Running shape-safe inference...")
            start_time = time.time()
            
            with torch.no_grad():
                # Simple chat call - no num_patches_list or complex parameters
                response = model.chat(
                    tokenizer, 
                    pixel_values, 
                    question, 
                    generation_config
                )
            
            inference_time = time.time() - start_time
            
            # Immediate cleanup
            del pixel_values
            torch.cuda.empty_cache()
            gc.collect()
            
            print(f"✅ Success in {inference_time:.2f}s")
            print(f"📝 Generated: {len(response)} chars")
            print(f"📄 Preview: {response[:200]}...")
            
            results.append({
                'prompt_id': f"prompt_{i}",
                'model': "InternVL3-8B-shape-safe",
                'mode': 'simple_processing',
                'urls_removed': system_urls + user_urls,
                'tensor_shape': str(pixel_values.shape) if 'pixel_values' in locals() else 'unknown',
                'generated_response': response,
                'generated_length': len(response),
                'inference_time': inference_time,
                'success': True
            })
            
        except RuntimeError as e:
            if "shape" in str(e) or "size" in str(e):
                print(f"❌ Shape error: {e}")
                print("💡 This indicates a tensor shape mismatch in the model")
            else:
                print(f"❌ Runtime error: {e}")
            
            torch.cuda.empty_cache()
            gc.collect()
            
            results.append({
                'prompt_id': f"prompt_{i}",
                'success': False,
                'error': f"Shape error: {str(e)}"
            })
            
        except Exception as e:
            print(f"❌ Error: {e}")
            torch.cuda.empty_cache()
            gc.collect()
            
            results.append({
                'prompt_id': f"prompt_{i}",
                'success': False,
                'error': str(e)
            })
        
        # Memory status
        if torch.cuda.is_available():
            memory_allocated = torch.cuda.memory_allocated() / 1024**3
            print(f"💾 GPU memory after prompt {i}: {memory_allocated:.1f}GB")
    
    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f'internvl3_shape_safe_results_{timestamp}.json'
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    # Summary
    successful = [r for r in results if r.get('success', False)]
    
    print(f"\n📊 SHAPE-SAFE SUMMARY:")
    print("=" * 60)
    print(f"🤖 Model: InternVL3-8B (shape-safe)")
    print(f"📊 Processed: {len(results)} prompts")
    print(f"✅ Successful: {len(successful)}")
    print(f"🔒 Mode: Simple processing (no dynamic tiles)")
    
    if successful:
        avg_time = sum(r['inference_time'] for r in successful) / len(successful)
        avg_length = sum(r['generated_length'] for r in successful) / len(successful)
        
        print(f"⏱️ Average time: {avg_time:.2f}s")
        print(f"📝 Average response: {avg_length:.0f} chars")
    
    print(f"\n💾 Results saved to: {results_file}")
    print("🎉 Shape-safe inference completed!")
    
    if len(successful) < len(results):
        print("\n💡 If still getting shape errors:")
        print("1. The model may have internal shape conflicts")
        print("2. Try different image sizes (224, 336, 448)")
        print("3. Check model version compatibility")

if __name__ == "__main__":
    main()
