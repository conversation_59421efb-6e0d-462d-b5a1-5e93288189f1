#!/usr/bin/env python3
"""
Hugging Face Authentication Setup
Helps set up authentication for gated models like Gemma-3n
"""

import os
from huggingface_hub import login, <PERSON>f<PERSON><PERSON>

def check_authentication():
    """Check current authentication status"""
    try:
        api = HfApi()
        user_info = api.whoami()
        print(f"✅ Currently authenticated as: {user_info['name']}")
        print(f"📧 Email: {user_info.get('email', 'N/A')}")
        return True, user_info
    except Exception as e:
        print(f"❌ Not authenticated: {e}")
        return False, None

def setup_environment_token():
    """Guide user to set up environment token"""
    print("\n🔧 ENVIRONMENT TOKEN SETUP")
    print("=" * 40)
    print("1. Go to: https://huggingface.co/settings/tokens")
    print("2. Click 'New token'")
    print("3. Name: 'VLM_Inference' (or any name)")
    print("4. Type: 'Read' (sufficient for model access)")
    print("5. Copy the generated token")
    print("\n6. Set environment variable:")
    print("   Linux/Mac: export HF_TOKEN='your_token_here'")
    print("   Windows: set HF_TOKEN=your_token_here")
    print("\n7. Or add to your shell profile (~/.bashrc, ~/.zshrc):")
    print("   echo 'export HF_TOKEN=\"your_token_here\"' >> ~/.bashrc")
    print("   source ~/.bashrc")

def interactive_login():
    """Perform interactive login"""
    print("\n🔐 INTERACTIVE LOGIN")
    print("=" * 30)
    
    try:
        # Check for existing token in environment
        hf_token = os.getenv('HF_TOKEN') or os.getenv('HUGGINGFACE_HUB_TOKEN')
        
        if hf_token:
            print("🔑 Found token in environment, attempting login...")
            login(token=hf_token)
        else:
            print("📝 No environment token found, starting interactive login...")
            print("You'll be prompted to enter your Hugging Face token.")
            login()
        
        # Verify authentication
        is_auth, user_info = check_authentication()
        if is_auth:
            print("🎉 Authentication successful!")
            return True
        else:
            print("❌ Authentication failed")
            return False
            
    except Exception as e:
        print(f"❌ Login error: {e}")
        return False

def check_model_access():
    """Check if user has access to Gemma-3n model"""
    print("\n🔍 CHECKING MODEL ACCESS")
    print("=" * 35)
    
    model_id = "google/gemma-3n-e2b-it"
    
    try:
        api = HfApi()
        model_info = api.model_info(model_id)
        print(f"✅ Access confirmed to: {model_id}")
        print(f"📊 Model info: {model_info.modelId}")
        return True
    except Exception as e:
        print(f"❌ Cannot access model: {e}")
        
        if "gated" in str(e) or "401" in str(e):
            print("\n🚨 MODEL ACCESS REQUIRED:")
            print(f"1. Visit: https://huggingface.co/{model_id}")
            print("2. Click 'Request access' or 'Agree and access repository'")
            print("3. Fill out the form if required")
            print("4. Wait for approval (usually within minutes)")
            print("5. Re-run this script to verify access")
        
        return False

def main():
    """Main authentication setup flow"""
    print("🔐 Hugging Face Authentication Setup")
    print("=" * 50)
    print("Setting up authentication for gated models like Gemma-3n")
    
    # Step 1: Check current authentication
    print("\n📋 STEP 1: Check Current Authentication")
    is_authenticated, user_info = check_authentication()
    
    # Step 2: Login if needed
    if not is_authenticated:
        print("\n📋 STEP 2: Authentication Required")
        
        # Show environment setup guide
        setup_environment_token()
        
        # Attempt interactive login
        print("\n🔄 Attempting login...")
        if not interactive_login():
            print("\n❌ Authentication setup failed")
            print("Please follow the environment token setup instructions above")
            return False
    
    # Step 3: Check model access
    print("\n📋 STEP 3: Check Model Access")
    if not check_model_access():
        print("\n❌ Model access setup incomplete")
        print("Please request access to the model and try again")
        return False
    
    # Success
    print("\n🎉 SETUP COMPLETE!")
    print("=" * 30)
    print("✅ Hugging Face authentication: OK")
    print("✅ Gemma-3n model access: OK")
    print("\nYou can now run:")
    print("  python test_gemma3n.py")
    print("  python gemma3n_inference.py")
    
    return True

if __name__ == "__main__":
    main()
