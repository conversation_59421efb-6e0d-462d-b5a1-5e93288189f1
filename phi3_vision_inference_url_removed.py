#!/usr/bin/env python3
"""
Phi-3-Vision Robust Inference
Handles the "decoder prompt too long" issue with aggressive truncation and context management
"""

import json
import time
import base64
from io import BytesIO
from datetime import datetime
from PIL import Image
from vllm import LLM, SamplingParams

def load_prompts_from_jsonl(file_path: str):
    """Load prompts from JSONL file"""
    print(f"📂 Loading prompts from {file_path}...")

    try:
        prompts = []
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Handle formatted JSON records
        brace_count = 0
        current_record = ""

        for line in content.split('\n'):
            if line.strip() == '{' and brace_count == 0:
                if current_record.strip():
                    try:
                        record = json.loads(current_record.strip())
                        prompts.append(record)
                    except json.JSONDecodeError:
                        pass
                current_record = line + '\n'
                brace_count = 1
            elif brace_count > 0:
                current_record += line + '\n'
                brace_count += line.count('{') - line.count('}')

                if brace_count == 0:
                    try:
                        record = json.loads(current_record.strip())
                        prompts.append(record)
                    except json.JSONDecodeError:
                        pass
                    current_record = ""

        if current_record.strip():
            try:
                record = json.loads(current_record.strip())
                prompts.append(record)
            except json.JSONDecodeError:
                pass

        print(f"✅ Loaded {len(prompts)} prompts")
        return prompts

    except Exception as e:
        print(f"❌ Error loading prompts: {str(e)}")
        return []

def prepare_image_conservative(base64_data: str) -> Image.Image:
    """Convert base64 to PIL Image with aggressive resizing to save tokens"""
    try:
        if base64_data.startswith('data:'):
            header, base64_content = base64_data.split(',', 1)
        else:
            base64_content = base64_data.strip()

        image_bytes = base64.b64decode(base64_content)
        image = Image.open(BytesIO(image_bytes))

        if image.mode != 'RGB':
            image = image.convert('RGB')

        # Aggressive resizing to minimize token usage
        max_size = 384  # Smaller than usual
        if max(image.size) > max_size:
            original_size = image.size
            image.thumbnail((max_size, max_size), Image.Resampling.LANCZOS)
            print(f"    📏 Aggressively resized: {original_size} → {image.size}")

        return image
    except Exception as e:
        print(f"    ❌ Image error: {e}")
        return None

def remove_urls_from_text(text: str) -> str:
    """Remove all URLs from text to reduce token count significantly"""
    import re

    # Remove various URL patterns
    url_patterns = [
        r'https?://[^\s<>"]+',  # Standard HTTP/HTTPS URLs
        r'www\.[^\s<>"]+',      # www URLs without protocol
        r'ftp://[^\s<>"]+',     # FTP URLs
        r'[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(?:/[^\s<>"]*)?',  # Domain-like patterns
    ]

    cleaned_text = text
    urls_removed = 0

    for pattern in url_patterns:
        matches = re.findall(pattern, cleaned_text)
        urls_removed += len(matches)
        cleaned_text = re.sub(pattern, '', cleaned_text)

    # Clean up extra whitespace left by URL removal
    cleaned_text = re.sub(r'\s+', ' ', cleaned_text)  # Multiple spaces to single
    cleaned_text = re.sub(r'\n\s*\n\s*\n', '\n\n', cleaned_text)  # Multiple newlines
    cleaned_text = cleaned_text.strip()

    return cleaned_text, urls_removed

def aggressive_truncate(system_prompt: str, user_prompt: str, target_chars: int) -> tuple:
    """Aggressively truncate prompts to fit in context"""

    print(f"  🔪 Aggressive truncation: {len(system_prompt) + len(user_prompt):,} → {target_chars:,} chars")

    # Very aggressive allocation
    system_ratio = 0.25  # Only 25% for system
    user_ratio = 0.75    # 75% for user (product data is more important)

    system_target = int(target_chars * system_ratio)
    user_target = int(target_chars * user_ratio)

    # System prompt truncation - keep only essential parts
    if len(system_prompt) > system_target:
        # Try to keep instructions only
        if "INSTRUCTIONS:" in system_prompt:
            instructions_start = system_prompt.find("INSTRUCTIONS:")
            instructions_part = system_prompt[instructions_start:]

            if len(instructions_part) <= system_target:
                truncated_system = instructions_part
            else:
                # Even instructions are too long, take first part
                truncated_system = instructions_part[:system_target]
        else:
            # No instructions section, take from end (usually instructions)
            truncated_system = system_prompt[-system_target:]
    else:
        truncated_system = system_prompt

    # User prompt truncation - preserve structure
    if len(user_prompt) > user_target:
        # Find key sections
        sections = user_prompt.split('\n\n')
        key_sections = []

        for section in sections:
            if any(keyword in section for keyword in ['Product details:', 'Enriched data:', 'Markdown content:']):
                key_sections.append(section)

        if key_sections:
            # Distribute space among key sections
            chars_per_section = user_target // len(key_sections)
            truncated_sections = []

            for section in key_sections:
                if len(section) <= chars_per_section:
                    truncated_sections.append(section)
                else:
                    # Take first part of section
                    truncated_sections.append(section[:chars_per_section])

            truncated_user = '\n\n'.join(truncated_sections)
        else:
            # No structure found, just truncate
            truncated_user = user_prompt[:user_target]
    else:
        truncated_user = user_prompt

    print(f"    System: {len(system_prompt):,} → {len(truncated_system):,} chars")
    print(f"    User: {len(user_prompt):,} → {len(truncated_user):,} chars")

    return truncated_system, truncated_user

def initialize_phi3_with_fallback():
    """Initialize Phi-3-Vision with progressive fallback - memory-aware context sizing"""

    # Memory-aware context attempts based on GPU memory limitations
    # vLLM error shows max possible is ~49,584 tokens with 18.16 GiB available
    context_attempts = [
        {"context": 120000, "gpu_util": 0.92, "description": "120k context"},
        {"context": 100000, "gpu_util": 0.9, "description": "100k context (near GPU memory limit)"},
        {"context": 90000, "gpu_util": 0.9, "description": "90k context (near GPU memory limit)"},
        {"context": 70000, "gpu_util": 0.85, "description": "70k context (near GPU memory limit)"},
        {"context": 60000, "gpu_util": 0.85, "description": "60k context (near GPU memory limit)"},
        {"context": 49000, "gpu_util": 0.85, "description": "49k context (near GPU memory limit)"},
        {"context": 45000, "gpu_util": 0.8, "description": "45k context (safe)"},
        {"context": 40000, "gpu_util": 0.75, "description": "40k context"},
        {"context": 35000, "gpu_util": 0.7, "description": "35k context"},
        {"context": 30000, "gpu_util": 0.65, "description": "30k context"},
        {"context": 25000, "gpu_util": 0.6, "description": "25k context"},
        {"context": 20000, "gpu_util": 0.55, "description": "20k context"},
        {"context": 16000, "gpu_util": 0.5, "description": "16k context (minimal)"}
    ]

    for attempt in context_attempts:
        try:
            print(f"  🔄 Trying {attempt['description']}...")

            # Calculate estimated KV cache memory needed
            estimated_kv_memory = (attempt["context"] / 1000) * 0.9  # Rough estimate: ~0.9 GB per 1k tokens
            print(f"    📊 Estimated KV cache needed: ~{estimated_kv_memory:.1f} GiB")

            llm = LLM(
                model="microsoft/Phi-3-vision-128k-instruct",
                trust_remote_code=True,
                max_model_len=attempt["context"],
                gpu_memory_utilization=attempt["gpu_util"],
                swap_space=4,  # Reduced swap to save memory
                max_num_seqs=1,
                limit_mm_per_prompt={"image": 2},  # Limit to 2 images
                enforce_eager=True,  # Use eager mode to save memory
                enable_paged_attention=True,  # EXPLICITLY ENABLE PAGED ATTENTION
            )

            print(f"  ✅ Success with {attempt['description']}")
            print(f"    💾 GPU utilization: {attempt['gpu_util']*100:.0f}%")
            return llm, attempt["context"]

        except Exception as e:
            error_msg = str(e)
            print(f"  ❌ Failed: {error_msg[:150]}...")

            # Check if it's a memory error and provide specific guidance
            if "KV cache" in error_msg and "memory" in error_msg:
                print(f"    💡 Memory issue detected - trying smaller context...")
            elif "CUDA out of memory" in error_msg:
                print(f"    💡 GPU memory exhausted - reducing utilization...")

            continue

    raise Exception("Could not initialize Phi-3-Vision with any context size")

def main():
    """Robust Phi-3-Vision inference"""
    print("🛡️ Phi-3-Vision Robust Inference")
    print("="*50)

    # Load prompts
    input_files = ['final_image_prompts_cleaned.jsonl', 'scrape_content_prompts.jsonl']
    prompts = None

    for input_file in input_files:
        prompts = load_prompts_from_jsonl(input_file)
        if prompts:
            print(f"✅ Loaded prompts from: {input_file}")
            break

    if not prompts:
        print("❌ No prompts loaded")
        return

    # Initialize model with fallback
    print(f"\n🤖 INITIALIZING PHI-3-VISION WITH FALLBACK")
    print("-" * 50)

    try:
        llm, max_context = initialize_phi3_with_fallback()
    except Exception as e:
        print(f"❌ All initialization attempts failed: {e}")
        return

    # Process prompts with aggressive truncation
    results = []
    max_prompts = 3

    for i, prompt_data in enumerate(prompts[:max_prompts], 1):
        print(f"\n{'='*20} PROMPT {i}/{max_prompts} {'='*20}")

        try:
            # Extract data
            system_prompt = prompt_data.get('SystemPrompt', '')
            user_prompt = prompt_data.get('UserPrompt', '')
            image_data_urls = prompt_data.get('Images', [])
            # actual_response = prompt_data.get('Response', '')  # Not used in robust mode

            print(f"📋 Original lengths:")
            print(f"  System: {len(system_prompt):,} chars")
            print(f"  User: {len(user_prompt):,} chars")
            print(f"  Images: {len(image_data_urls)}")

            # Remove URLs first to maximize available context
            print(f"  🔗 Removing URLs to maximize context...")
            cleaned_system, system_urls = remove_urls_from_text(system_prompt)
            cleaned_user, user_urls = remove_urls_from_text(user_prompt)

            total_urls_removed = system_urls + user_urls
            total_chars_saved = (len(system_prompt) - len(cleaned_system)) + (len(user_prompt) - len(cleaned_user))

            print(f"  📝 URL removal results:")
            print(f"    URLs removed: {total_urls_removed}")
            print(f"    Characters saved: {total_chars_saved:,}")
            print(f"    System: {len(system_prompt):,} → {len(cleaned_system):,} chars")
            print(f"    User: {len(user_prompt):,} → {len(cleaned_user):,} chars")

            # Use cleaned prompts
            system_prompt = cleaned_system
            user_prompt = cleaned_user

            # Process only 2 images to save tokens
            processed_images = []
            for j, img_data in enumerate(image_data_urls[:2]):
                image = prepare_image_conservative(img_data)
                if image:
                    processed_images.append(image)
                    print(f"    ✓ Image {j+1}: {image.size}")

            if not processed_images:
                print("  ❌ No valid images")
                continue

            # Calculate safe token budget based on actual available context
            # After URL removal, we can be more generous with text allocation
            reserved_for_images = len(processed_images) * 1200  # Reduced estimate after URL removal
            reserved_for_response = 2000  # Space for good response
            available_for_text = max_context - reserved_for_images - reserved_for_response

            # Convert to characters (more generous after URL removal)
            target_chars = int(available_for_text * 2.5)  # 2.5 chars per token (better after URL removal)

            print(f"  📊 Token budget (after URL removal):")
            print(f"    Max context: {max_context:,}")
            print(f"    Reserved for images: {reserved_for_images:,}")
            print(f"    Reserved for response: {reserved_for_response:,}")
            print(f"    Available for text: {available_for_text:,} tokens")
            print(f"    Target text chars: {target_chars:,}")

            # Check if we even need truncation after URL removal
            current_chars = len(system_prompt) + len(user_prompt)
            if current_chars <= target_chars:
                print(f"  🎉 No truncation needed! Current: {current_chars:,}, Target: {target_chars:,}")
                final_system = system_prompt
                final_user = user_prompt
            else:
                print(f"  ⚠️ Truncation needed: {current_chars:,} → {target_chars:,} chars")

                # Apply aggressive truncation only if needed
                final_system, final_user = aggressive_truncate(system_prompt, user_prompt, target_chars)

            # Create Phi-3-Vision prompt
            image_tokens = "".join([f"<|image_{i+1}|>" for i in range(len(processed_images))])
            full_prompt = f"<|user|>\n{image_tokens}\n{final_system}\n\n{final_user}<|end|>\n<|assistant|>\n"

            print(f"  📏 Final prompt: {len(full_prompt):,} chars")

            # Conservative sampling parameters
            sampling_params = SamplingParams(
                temperature=0.1,
                max_tokens=800,  # Conservative output length
                stop=["<|end|>", "<|user|>", "<|assistant|>"]
            )

            # Run inference
            print("  🚀 Running robust inference...")
            start_time = time.time()

            outputs = llm.generate(
                [{
                    "prompt": full_prompt,
                    "multi_modal_data": {"image": processed_images}
                }],
                sampling_params=sampling_params
            )

            inference_time = time.time() - start_time

            if outputs and outputs[0].outputs:
                generated_text = outputs[0].outputs[0].text.strip()

                print(f"  ✅ Success in {inference_time:.2f}s")
                print(f"  📝 Generated: {len(generated_text):,} chars")
                print(f"  📄 Preview: {generated_text[:200]}...")

                results.append({
                    'prompt_id': f"prompt_{i}",
                    'max_context_used': max_context,
                    'original_system_length': len(system_prompt),
                    'original_user_length': len(user_prompt),
                    'final_system_length': len(final_system),
                    'final_user_length': len(final_user),
                    'compression_ratio': (len(final_system) + len(final_user)) / (len(system_prompt) + len(user_prompt)),
                    'generated_response': generated_text,
                    'inference_time': inference_time,
                    'success': True
                })

            else:
                print("  ❌ No output generated")

        except Exception as e:
            print(f"  ❌ Error: {e}")
            results.append({
                'prompt_id': f"prompt_{i}",
                'success': False,
                'error': str(e)
            })

    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f'phi3_robust_results_{timestamp}.json'

    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)

    # Summary
    successful = [r for r in results if r.get('success', False)]
    print(f"\n📊 SUMMARY:")
    print(f"  Context used: {max_context:,} tokens")
    print(f"  Processed: {len(results)} prompts")
    print(f"  Successful: {len(successful)}")

    if successful:
        avg_compression = sum(r['compression_ratio'] for r in successful) / len(successful)
        avg_time = sum(r['inference_time'] for r in successful) / len(successful)

        print(f"  Average compression: {avg_compression:.1%}")
        print(f"  Average time: {avg_time:.2f}s")

        print(f"\n📋 COMPRESSION DETAILS:")
        for result in successful:
            original_total = result['original_system_length'] + result['original_user_length']
            final_total = result['final_system_length'] + result['final_user_length']
            print(f"    {result['prompt_id']}: {original_total:,} → {final_total:,} chars ({result['compression_ratio']:.1%})")

    print(f"\n💾 Results saved to: {output_file}")
    print(f"🎉 Robust inference completed!")

if __name__ == "__main__":
    main()
