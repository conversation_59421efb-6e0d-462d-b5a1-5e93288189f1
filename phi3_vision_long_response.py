#!/usr/bin/env python3
"""
Phi-3-Vision Long Response Inference
Optimized for generating very long responses (up to 16k characters)
"""

import json
import time
import base64
from io import BytesIO
from datetime import datetime
from PIL import Image
from vllm import LLM, SamplingParams

def load_prompts_from_jsonl(file_path: str):
    """Load prompts from JSONL file"""
    print(f"📂 Loading prompts from {file_path}...")

    try:
        prompts = []
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Parse formatted JSON records
        brace_count = 0
        current_record = ""

        for line in content.split('\n'):
            if line.strip() == '{' and brace_count == 0:
                if current_record.strip():
                    try:
                        record = json.loads(current_record.strip())
                        prompts.append(record)
                    except json.JSONDecodeError:
                        pass
                current_record = line + '\n'
                brace_count = 1
            elif brace_count > 0:
                current_record += line + '\n'
                brace_count += line.count('{') - line.count('}')

                if brace_count == 0:
                    try:
                        record = json.loads(current_record.strip())
                        prompts.append(record)
                    except json.JSONDecodeError:
                        pass
                    current_record = ""

        # Add last record if exists
        if current_record.strip():
            try:
                record = json.loads(current_record.strip())
                prompts.append(record)
            except json.JSONDecodeError:
                pass

        print(f"✅ Loaded {len(prompts)} prompts from JSONL")
        return prompts

    except Exception as e:
        print(f"❌ Error loading prompts: {str(e)}")
        return []

def prepare_image_for_phi3(base64_data: str) -> Image.Image:
    """Convert base64 to PIL Image optimized for Phi-3-Vision"""
    try:
        if base64_data.startswith('data:'):
            header, base64_content = base64_data.split(',', 1)
        else:
            base64_content = base64_data.strip()

        image_bytes = base64.b64decode(base64_content)
        image = Image.open(BytesIO(image_bytes))

        if image.mode != 'RGB':
            image = image.convert('RGB')

        # Conservative resizing for long responses (save tokens for text)
        max_size = 512
        if max(image.size) > max_size:
            original_size = image.size
            image.thumbnail((max_size, max_size), Image.Resampling.LANCZOS)
            print(f"    📏 Resized: {original_size} → {image.size}")

        return image
    except Exception as e:
        print(f"    ❌ Image error: {e}")
        return None

def create_phi3_prompt_for_long_response(system_prompt: str, user_prompt: str, num_images: int) -> str:
    """Create Phi-3-Vision prompt optimized for long responses"""

    # Add instruction for detailed response
    enhanced_system = system_prompt + "\n\nIMPORTANT: Provide a comprehensive, detailed response with thorough analysis. Include all relevant information and explanations."

    # Phi-3-Vision format with image tokens
    image_tokens = "".join([f"<|image_{i+1}|>" for i in range(num_images)])

    return f"<|user|>\n{image_tokens}\n{enhanced_system}\n\n{user_prompt}<|end|>\n<|assistant|>\n"

def estimate_tokens_for_long_response(prompt: str, num_images: int) -> dict:
    """Estimate token usage optimized for long responses"""
    # Conservative estimates for Phi-3-Vision
    text_tokens = len(prompt) // 3.5  # Phi-3 tokenization
    image_tokens = num_images * 400   # Reduced estimate for smaller images
    total_input_tokens = text_tokens + image_tokens

    return {
        'input_text_tokens': int(text_tokens),
        'input_image_tokens': image_tokens,
        'total_input_tokens': int(total_input_tokens),
        'available_for_output': 60000 - int(total_input_tokens),  # Assuming 60k context
        'can_generate_16k_chars': (60000 - int(total_input_tokens)) > 4000  # ~4k tokens for 16k chars
    }

def calculate_similarity(text1: str, text2: str) -> float:
    """Calculate text similarity"""
    if not text1 or not text2:
        return 0.0

    import difflib
    matcher = difflib.SequenceMatcher(None, text1.lower().strip(), text2.lower().strip())
    return matcher.ratio()

def main():
    """Main long response inference"""
    print("📝 Phi-3-Vision Long Response Inference (16k+ characters)")
    print("="*70)

    # Load prompts
    input_files = ['final_image_prompts_cleaned.jsonl', 'scrape_content_prompts.jsonl']
    prompts = None

    for input_file in input_files:
        prompts = load_prompts_from_jsonl(input_file)
        if prompts:
            print(f"✅ Loaded prompts from: {input_file}")
            break

    if not prompts:
        print("❌ No prompts loaded, exiting")
        return

    print(f"📊 Found {len(prompts)} prompts total")

    # Initialize Phi-3-Vision with optimized settings for long responses
    print(f"\n🤖 INITIALIZING PHI-3-VISION FOR LONG RESPONSES")
    print("-" * 60)

    try:
        # Use conservative context to leave room for long outputs
        max_context = 60000  # 60k context

        llm = LLM(
            model="microsoft/Phi-3-vision-128k-instruct",
            trust_remote_code=True,
            max_model_len=max_context,
            gpu_memory_utilization=0.75,
            swap_space=8,
            max_num_seqs=1,
            limit_mm_per_prompt={"image": 2},  # Limit images to save tokens for output
            # Note: Paged attention is enabled by default in modern vLLM
        )
        print(f"✅ Phi-3-Vision loaded with {max_context:,} token context")
        print(f"🎯 Optimized for generating long, detailed responses")

    except Exception as e:
        print(f"❌ Model loading failed: {str(e)}")
        return

    # Process prompts with focus on long responses
    results = []
    max_prompts = 3  # Test with first 3

    for i, prompt_data in enumerate(prompts[:max_prompts], 1):
        print(f"\n{'='*25} PROMPT {i}/{max_prompts} {'='*25}")

        try:
            # Extract data
            system_prompt = prompt_data.get('SystemPrompt', '')
            user_prompt = prompt_data.get('UserPrompt', '')
            image_data_urls = prompt_data.get('Images', [])
            actual_response = prompt_data.get('Response', '')

            print(f"📋 Prompt {i}:")
            print(f"  System: {len(system_prompt):,} chars")
            print(f"  User: {len(user_prompt):,} chars")
            print(f"  Images available: {len(image_data_urls)}")
            print(f"  Expected response: {len(actual_response):,} chars")

            # Process images (limit to 2 for token efficiency)
            processed_images = []
            for j, img_data in enumerate(image_data_urls[:2]):
                image = prepare_image_for_phi3(img_data)
                if image:
                    processed_images.append(image)
                    print(f"    ✓ Image {j+1}: {image.size}")

            if not processed_images:
                print("  ❌ No valid images")
                continue

            # Create prompt optimized for long responses
            full_prompt = create_phi3_prompt_for_long_response(
                system_prompt, user_prompt, len(processed_images)
            )

            # Estimate tokens and output capacity
            token_estimate = estimate_tokens_for_long_response(full_prompt, len(processed_images))
            print(f"  🔢 Token analysis:")
            print(f"      Input tokens: {token_estimate['total_input_tokens']:,}")
            print(f"      Available for output: {token_estimate['available_for_output']:,}")
            print(f"      Can generate 16k chars: {'✅' if token_estimate['can_generate_16k_chars'] else '❌'}")

            # Sampling parameters optimized for LONG responses
            max_output_tokens = min(token_estimate['available_for_output'] - 1000, 6000)  # Up to 6k tokens = ~24k chars

            sampling_params = SamplingParams(
                temperature=0.1,  # Low temperature for consistency
                top_p=0.9,
                max_tokens=max_output_tokens,  # MUCH higher for long responses
                stop=["<|end|>", "<|user|>", "<|assistant|>"],
                # Additional parameters for longer generation
                repetition_penalty=1.05,  # Slight penalty to avoid repetition in long text
            )

            print(f"  📝 Max output tokens: {max_output_tokens:,} (~{max_output_tokens * 4:,} chars)")

            # Run inference
            print("  🚀 Running long response inference...")
            start_time = time.time()

            outputs = llm.generate(
                [{
                    "prompt": full_prompt,
                    "multi_modal_data": {"image": processed_images}
                }],
                sampling_params=sampling_params
            )

            inference_time = time.time() - start_time

            if outputs and outputs[0].outputs:
                generated_text = outputs[0].outputs[0].text.strip()

                print(f"  ✅ Success in {inference_time:.2f}s")
                print(f"  📝 Generated: {len(generated_text):,} characters")
                print(f"  📊 Target was: 16,000+ characters")
                print(f"  🎯 Achievement: {len(generated_text)/16000*100:.1f}% of 16k target")

                # Show response preview
                print(f"  📄 Response preview (first 300 chars):")
                print(f"      {generated_text[:300]}...")

                # Calculate similarity with actual response
                similarity = 0.0
                if actual_response:
                    similarity = calculate_similarity(generated_text, actual_response)
                    print(f"  🔍 Similarity to expected: {similarity:.3f}")

                # Check if we achieved 16k+ characters
                achieved_16k = len(generated_text) >= 16000
                print(f"  🏆 16k+ characters: {'✅ ACHIEVED' if achieved_16k else '❌ Not reached'}")

                # Store result
                results.append({
                    'prompt_id': f"prompt_{i}",
                    'system_prompt_length': len(system_prompt),
                    'user_prompt_length': len(user_prompt),
                    'num_images': len(processed_images),
                    'input_tokens_estimated': token_estimate['total_input_tokens'],
                    'max_output_tokens': max_output_tokens,
                    'generated_response': generated_text,
                    'generated_length': len(generated_text),
                    'achieved_16k': achieved_16k,
                    'actual_response': actual_response,
                    'similarity': similarity,
                    'inference_time': inference_time,
                    'success': True,
                    'model': 'phi3-vision-128k-long-response'
                })

            else:
                print("  ❌ No output generated")
                results.append({
                    'prompt_id': f"prompt_{i}",
                    'success': False,
                    'error': 'No output generated'
                })

        except Exception as e:
            print(f"  ❌ Error: {e}")
            results.append({
                'prompt_id': f"prompt_{i}",
                'success': False,
                'error': str(e)
            })

    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f'phi3_long_response_results_{timestamp}.json'

    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)

    # Summary
    successful = [r for r in results if r.get('success', False)]
    achieved_16k = [r for r in successful if r.get('achieved_16k', False)]

    print(f"\n📊 FINAL SUMMARY:")
    print("="*50)
    print(f"  Total prompts: {len(results)}")
    print(f"  Successful: {len(successful)}")
    print(f"  Achieved 16k+ chars: {len(achieved_16k)}")
    print(f"  Success rate: {len(successful)/len(results)*100:.1f}%")
    print(f"  16k achievement rate: {len(achieved_16k)/len(successful)*100:.1f}%" if successful else "  16k achievement rate: 0%")

    if successful:
        avg_length = sum(r['generated_length'] for r in successful) / len(successful)
        max_length = max(r['generated_length'] for r in successful)
        min_length = min(r['generated_length'] for r in successful)
        avg_time = sum(r['inference_time'] for r in successful) / len(successful)

        print(f"  Average response length: {avg_length:,.0f} chars")
        print(f"  Longest response: {max_length:,} chars")
        print(f"  Shortest response: {min_length:,} chars")
        print(f"  Average inference time: {avg_time:.2f}s")

        if achieved_16k:
            print(f"\n🏆 16K+ CHARACTER RESPONSES:")
            for result in achieved_16k:
                print(f"    {result['prompt_id']}: {result['generated_length']:,} chars")

    print(f"\n💾 Results saved to: {output_file}")
    print(f"\n🎉 LONG RESPONSE INFERENCE COMPLETED!")

    if achieved_16k:
        print(f"✅ Successfully generated {len(achieved_16k)} responses with 16k+ characters!")
    else:
        print(f"⚠️ No responses reached 16k characters. Consider:")
        print(f"   - Increasing max_tokens further")
        print(f"   - Using more detailed prompts")
        print(f"   - Adjusting temperature/top_p")

if __name__ == "__main__":
    main()
