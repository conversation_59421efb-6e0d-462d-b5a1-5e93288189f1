# Max Tokens Explained

## 🎯 **What Are "Max Tokens"?**

### **Definition:**
**Max tokens** refers to the maximum number of tokens that a language model can process in a single inference call. This includes both **input tokens** (your prompt) and **output tokens** (the generated response).

## 📊 **Two Different "Max Tokens" Concepts**

### **1. Model Context Length (`max_model_len`)**
```python
llm = LLM(
    model="microsoft/Phi-3-vision-128k-instruct",
    max_model_len=65000,  # ← This is the TOTAL context window
)
```

**What it controls:**
- **Total tokens** the model can handle (input + output)
- **Memory allocation** for the model
- **Context window size**

### **2. Output Generation Length (`max_tokens` in SamplingParams)**
```python
sampling_params = SamplingParams(
    temperature=0.3,
    max_tokens=2000,  # ← This is the OUTPUT length limit
)
```

**What it controls:**
- **Maximum response length** the model will generate
- **Stops generation** when this limit is reached
- **Does not affect input processing**

## 🔢 **Token Breakdown for Your Case**

### **Your Current Setup:**
```
Model Context (max_model_len): 65,000 tokens
├── Input Prompt: ~54,400 tokens
│   ├── System prompt: ~18,000 tokens
│   ├── User prompt: ~34,000 tokens
│   └── Images (2): ~2,400 tokens
├── Output Response: 2,000 tokens (max_tokens setting)
└── Safety Buffer: ~8,600 tokens
```

### **Token Calculation:**
```
Total Available: 65,000 tokens
Used for Input: 54,400 tokens (84%)
Reserved for Output: 2,000 tokens (3%)
Safety Buffer: 8,600 tokens (13%)
```

## 📏 **What Is a "Token"?**

### **For Text:**
- **English**: ~3-4 characters per token
- **"Hello world"** = 2 tokens
- **"The quick brown fox"** = 4 tokens
- **Your 130k char prompt** ≈ 52k tokens

### **For Images:**
- **Each image** = ~1,200 tokens (varies by size/aspect ratio)
- **2 images** = ~2,400 tokens
- **5 images** = ~6,000 tokens

### **Examples:**
```
Text: "Analyze this product" = 3 tokens
Image: [Product photo] = 1,200 tokens
Total: 1,203 tokens
```

## ⚖️ **Context Window Trade-offs**

### **Larger Context (`max_model_len`):**
```
✅ Benefits:
- Can process longer prompts
- No truncation needed
- More detailed context

❌ Costs:
- More GPU memory required
- Slower inference
- Higher computational cost
```

### **Smaller Context:**
```
✅ Benefits:
- Less GPU memory
- Faster inference
- More stable

❌ Costs:
- May need truncation
- Limited prompt length
- Less context available
```

## 🎛️ **Your Current Settings Explained**

### **In `utils/model_initializer.py`:**
```python
max_model_len=65000  # Total context window
```
**Meaning:** The model can handle up to 65,000 tokens total (input + output)

### **In `phi3_vision_inference_clean.py`:**
```python
max_tokens=2000  # Output length limit
```
**Meaning:** The response will be at most 2,000 tokens (~8,000 characters)

## 📊 **Token Limits by Model**

### **Phi-3-Vision:**
- **Theoretical max**: 128,000 tokens
- **Practical max**: 65,000 tokens (GPU memory limited)
- **Your usage**: 54,400 tokens (fits comfortably)

### **Other Models:**
```
Model                    Max Context    Practical Limit
Phi-3-Vision-128k       128,000        65,000 tokens
LLaVA-1.5-7B           4,096          4,000 tokens
LLaVA-v1.6-Mistral     32,768         30,000 tokens
Qwen2-VL-7B            32,768         30,000 tokens
```

## 🔧 **How to Adjust Token Limits**

### **1. Increase Output Length:**
```python
# In phi3_vision_inference_clean.py
sampling_params = SamplingParams(
    temperature=0.3,
    max_tokens=4000,  # ← Increase for longer responses
)
```

### **2. Increase Context Window:**
```python
# In utils/model_initializer.py
context_attempts = [
    {"context": 100000, ...},  # ← Try even larger contexts
    {"context": 80000, ...},
    # ...
]
```

### **3. Monitor Token Usage:**
```python
# The script now shows:
📊 Token estimation:
  Text: 52,006 tokens
  Images: 2,400 tokens
  Total: 54,406 tokens
  Context available: 65,000 tokens
```

## ⚠️ **Common Token Issues**

### **1. "Decoder prompt too long"**
```
❌ Problem: Input tokens > max_model_len
✅ Solution: Increase max_model_len or reduce input
```

### **2. "Response cut off"**
```
❌ Problem: Output hits max_tokens limit
✅ Solution: Increase max_tokens in SamplingParams
```

### **3. "CUDA out of memory"**
```
❌ Problem: max_model_len too large for GPU
✅ Solution: Reduce max_model_len or use smaller GPU utilization
```

## 🎯 **Optimal Settings for Your Use Case**

### **Current (Working) Settings:**
```python
# Model context
max_model_len = 65000  # Fits your 54k token prompts

# Output length  
max_tokens = 2000  # Generates ~8k character responses

# Result: ✅ Works perfectly for your 168k char prompts
```

### **If You Want Longer Responses:**
```python
# Increase output tokens
max_tokens = 4000  # ~16k character responses

# Ensure total fits in context:
# Input (54k) + Output (4k) = 58k < 65k ✅
```

### **If You Want Even Longer Prompts:**
```python
# Try larger context (if GPU allows)
max_model_len = 80000  # For future longer prompts

# Monitor GPU memory usage
gpu_memory_utilization = 0.7  # More conservative
```

## 🎉 **Summary**

### **Max Tokens = Two Different Things:**

1. **`max_model_len`** (Context Window):
   - **Total tokens** model can handle
   - **Your setting**: 65,000 tokens
   - **Your usage**: 54,400 tokens ✅

2. **`max_tokens`** (Output Limit):
   - **Response length** limit
   - **Your setting**: 2,000 tokens (~8k chars)
   - **Can be increased** if you want longer responses

### **Your Current Status:**
- ✅ **Input fits**: 54k tokens < 65k context
- ✅ **Output reasonable**: 2k tokens (~8k chars)
- ✅ **No truncation**: Full prompts processed
- ✅ **Room to grow**: 10k token buffer available

**Your token settings are well-optimized for your 168k character prompts!** 🚀
