import psycopg2
import json
import re
from datetime import datetime
import logging
from typing import List, Dict, Any
import requests
import base64
from io import BytesIO
from PIL import Image
import hashlib
import time

# Configure logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ScrapeContentDataLoader:
    def __init__(self, db_config: Dict[str, Any], schema_file: str = "response_schema.json"):
        """
        Initialize the data loader with database configuration and response schema.

        Args:
            db_config (Dict[str, Any]): Database connection parameters.
            schema_file (str): Path to the response schema JSON file.
        """
        self.db_config = db_config
        self.connection = None
        self.cursor = None
        self.schema_file = schema_file
        self.response_schema = self.load_response_schema()

    def load_response_schema(self) -> Dict[str, Any]:
        """
        Load the response schema from the JSON file.

        Returns:
            Dict[str, Any]: The response schema dictionary.
        """
        try:
            with open(self.schema_file, 'r', encoding='utf-8') as f:
                schema = json.load(f)
            logger.info(f"Successfully loaded response schema from {self.schema_file}")
            return schema
        except FileNotFoundError:
            logger.error(f"Schema file {self.schema_file} not found")
            raise
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in schema file {self.schema_file}: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Error loading schema file {self.schema_file}: {str(e)}")
            raise

    def generate_system_prompt(self) -> str:
        """
        Generate a comprehensive system prompt based on the response schema.

        Returns:
            str: The system prompt instructing the AI to follow the schema.
        """
        schema_json = json.dumps(self.response_schema, indent=2, ensure_ascii=False)

        system_prompt = f"""You are a helpful assistant specialized in extracting comprehensive product information from images and text data. Your task is to analyze product information and provide structured data in JSON format according to the detailed schema provided below.

RESPONSE SCHEMA:
{schema_json}

INSTRUCTIONS:
1. You are provided with product images and product details fetched from online sources in $PRODUCT_DATA
2. You are also provided with products data from other sources which might be similar to the original product in $ENRICHED_DATA
3. Your job is to go through the images of the product, $PRODUCT_DATA and $ENRICHED_DATA and extract information according to the comprehensive schema provided above
4. Extract as much information as possible from the available data sources
5. For fields where information is not available in the provided data, you may:
   - Use your knowledge to make reasonable inferences based on the product type and available information
   - Set appropriate null values or empty arrays/objects as specified in the schema
   - For "qualified" fields, provide AI-inferred values based on your understanding of the product
6. Pay special attention to:
   - Nutritional information (both stated and qualified/inferred values)
   - Ingredient lists (including nested ingredients)
   - Allergen information (both stated and qualified/inferred)
   - Product claims and certifications
   - Clean label attributes
7. Absolutely avoid using ```json or ``` markdown formatting in the final response
8. If you get multiple images which are unrelated to each other, provide output only for one product
9. Your output must be a valid JSON string that conforms to the provided schema
10. Do not output the schema itself - only provide the extracted data in JSON format

Remember: This data will be used for fine-tuning, so accuracy and completeness are crucial. Extract all available information and make reasonable inferences where appropriate."""

        return system_prompt

    def download_image(self, url: str, max_retries: int = 3) -> str:
        """
        Download an image from URL and return it as base64 encoded string.

        Args:
            url (str): Image URL to download
            max_retries (int): Maximum number of retry attempts

        Returns:
            str: Base64 encoded image string, or empty string if failed
        """
        if not url or not url.startswith('http'):
            return ""

        for attempt in range(max_retries):
            try:
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                }

                response = requests.get(url, headers=headers, timeout=10)
                response.raise_for_status()

                # Verify it's an image
                if not response.headers.get('content-type', '').startswith('image/'):
                    logger.warning(f"URL {url} does not return an image (content-type: {response.headers.get('content-type')})")
                    return ""

                # Convert to base64
                image_data = response.content
                base64_image = base64.b64encode(image_data).decode('utf-8')

                # Get image format from content-type or URL
                content_type = response.headers.get('content-type', 'image/jpeg')
                image_format = content_type.split('/')[-1].lower()
                if image_format not in ['jpeg', 'jpg', 'png', 'gif', 'webp']:
                    image_format = 'jpeg'

                # Create data URL format for VLM training
                data_url = f"data:{content_type};base64,{base64_image}"

                logger.debug(f"Successfully downloaded image from {url} (size: {len(image_data)} bytes)")
                return data_url

            except requests.exceptions.RequestException as e:
                logger.warning(f"Attempt {attempt + 1} failed to download image from {url}: {str(e)}")
                if attempt < max_retries - 1:
                    time.sleep(1)  # Wait before retry
                continue
            except Exception as e:
                logger.error(f"Unexpected error downloading image from {url}: {str(e)}")
                break

        logger.error(f"Failed to download image from {url} after {max_retries} attempts")
        return ""

    def clean_markdown_content(self, markdown_content: str) -> str:
        """
        Remove image URLs and markdown image syntax from markdown content.

        Args:
            markdown_content (str): Original markdown content

        Returns:
            str: Cleaned markdown content without image URLs
        """
        if not markdown_content:
            return markdown_content

        # Remove markdown image syntax: ![alt text](url)
        markdown_content = re.sub(r'!\[.*?\]\([^)]+\)', '', markdown_content)

        # Remove standalone image URLs (common image extensions)
        image_url_pattern = r'https?://[^\s<>"]+\.(?:jpg|jpeg|png|gif|webp|bmp)(?:\?[^\s]*)?'
        markdown_content = re.sub(image_url_pattern, '', markdown_content, flags=re.IGNORECASE)

        # Remove HTML img tags
        markdown_content = re.sub(r'<img[^>]*>', '', markdown_content, flags=re.IGNORECASE)

        # Clean up multiple consecutive newlines and spaces
        markdown_content = re.sub(r'\n\s*\n\s*\n', '\n\n', markdown_content)
        markdown_content = re.sub(r'[ \t]+', ' ', markdown_content)

        # Remove leading/trailing whitespace from lines
        lines = [line.strip() for line in markdown_content.split('\n')]
        markdown_content = '\n'.join(lines)

        # Remove excessive blank lines at start and end
        markdown_content = markdown_content.strip()

        return markdown_content

    def extract_and_download_images(self, product_data: Dict[str, Any], markdown_content: str = "") -> List[str]:
        """
        Extract image URLs from product data and download them as base64 encoded strings.

        Args:
            product_data (Dict[str, Any]): Product data containing image URLs
            markdown_content (str): Markdown content that might contain additional images

        Returns:
            List[str]: List of base64 encoded image data URLs
        """
        image_urls = []
        downloaded_images = []

        # Extract from product_data.images_url
        if 'images_url' in product_data and isinstance(product_data['images_url'], list):
            image_urls.extend(product_data['images_url'])

        # Extract from markdown content (basic regex for image URLs)
        if markdown_content:
            # Find URLs that look like images
            image_url_pattern = r'https?://[^\s<>"]+\.(?:jpg|jpeg|png|gif|webp|bmp)'
            found_urls = re.findall(image_url_pattern, markdown_content, re.IGNORECASE)
            image_urls.extend(found_urls)

        # Remove duplicates while preserving order
        seen = set()
        unique_urls = []
        for url in image_urls:
            if url not in seen:
                seen.add(url)
                unique_urls.append(url)

        # Download images (limit to first 5 to avoid too much data)
        max_images = 5
        logger.info(f"Found {len(unique_urls)} image URLs, downloading first {min(len(unique_urls), max_images)}")

        for i, url in enumerate(unique_urls[:max_images]):
            logger.debug(f"Downloading image {i+1}/{min(len(unique_urls), max_images)}: {url}")
            base64_image = self.download_image(url)
            if base64_image:
                downloaded_images.append(base64_image)

            # Small delay between downloads to be respectful
            if i < len(unique_urls) - 1:
                time.sleep(0.5)

        logger.info(f"Successfully downloaded {len(downloaded_images)} images out of {min(len(unique_urls), max_images)} attempted")
        return downloaded_images

    def format_json_with_gaps(self, data: Dict[str, Any]) -> str:
        """
        Format JSON with line gaps between major sections for better readability.

        Args:
            data (Dict[str, Any]): The prompt-response data to format

        Returns:
            str: Formatted JSON string with line gaps
        """
        # Create the formatted structure with line gaps
        formatted_parts = []
        formatted_parts.append("{")

        # SystemPrompt section
        formatted_parts.append("")  # Empty line
        formatted_parts.append('  "SystemPrompt": ' + json.dumps(data["SystemPrompt"], ensure_ascii=False) + ",")

        # UserPrompt section
        formatted_parts.append("")  # Empty line
        formatted_parts.append('  "UserPrompt": ' + json.dumps(data["UserPrompt"], ensure_ascii=False) + ",")

        # Images section
        formatted_parts.append("")  # Empty line
        formatted_parts.append('  "Images": [')
        for i, image in enumerate(data["Images"]):
            comma = "," if i < len(data["Images"]) - 1 else ""
            formatted_parts.append('    ' + json.dumps(image, ensure_ascii=False) + comma)
        formatted_parts.append('  ],')

        # Response section
        formatted_parts.append("")  # Empty line
        formatted_parts.append('  "Response": ' + json.dumps(data["Response"], ensure_ascii=False))

        formatted_parts.append("")  # Empty line
        formatted_parts.append("}")

        return '\n'.join(formatted_parts)

    def get_schema_statistics(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Generate statistics about schema coverage in the extracted data.

        Args:
            data (List[Dict[str, Any]]): List of processed data

        Returns:
            Dict[str, Any]: Statistics about schema field coverage
        """
        stats = {
            'total_records': len(data),
            'field_coverage': {},
            'section_coverage': {}
        }

        # Define main schema sections
        main_sections = ['generalData', 'servingSize', 'nutritionalInformation',
                        'ingredients', 'claims', 'NPI 2.0 Food Packages - Allergens & Intolerances',
                        'cleanLabel']

        for section in main_sections:
            section_count = 0
            for record in data:
                try:
                    extracted_data = json.loads(record.get('extracted_json', '{}'))
                    # Check both root level and nested under 'analysed_data'
                    if (section in extracted_data and extracted_data[section]) or \
                       ('analysed_data' in extracted_data and
                        section in extracted_data['analysed_data'] and
                        extracted_data['analysed_data'][section]):
                        section_count += 1
                except:
                    continue

            coverage_pct = (section_count / len(data) * 100) if data else 0
            stats['section_coverage'][section] = {
                'count': section_count,
                'percentage': round(coverage_pct, 2)
            }

        logger.info(f"Schema Coverage Statistics:")
        logger.info(f"Total records processed: {stats['total_records']}")
        for section, info in stats['section_coverage'].items():
            logger.info(f"  {section}: {info['count']}/{stats['total_records']} ({info['percentage']}%)")

        return stats

    def connect(self) -> None:
        """Establish a connection to the PostgreSQL database."""
        try:
            self.connection = psycopg2.connect(**self.db_config)
            self.cursor = self.connection.cursor()
            logger.info("Successfully connected to the database.")
        except Exception as e:
            logger.error(f"Failed to connect to the database: {str(e)}")
            raise

    def close_connection(self) -> None:
        """Close the database cursor and connection."""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
            logger.info("Database connection closed.")

    def fetch_scrape_content(self, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Fetch specified fields from the scrape_content table, joining with extraction_results,
        including only rows where all fields are non-null, and return in JSON-compatible format.

        Args:
            limit (int): Number of rows to fetch (default is 50).

        Returns:
            List[Dict[str, Any]]: List of dictionaries containing the fetched data with no null fields.
        """
        try:
            # Query to fetch fields from scrape_content and extracted_json from extraction_results
            query = """
                SELECT
                    sc.id,
                    sc.product_data,
                    sc.markdown_content,
                    sc.enriched_data,
                    sc.extracted_data,
                    er.extracted_json
                FROM scrape_content sc
                LEFT JOIN extraction_results er ON sc.id = er.id
                WHERE
                    sc.product_data IS NOT NULL AND
                    sc.markdown_content IS NOT NULL AND
                    sc.enriched_data IS NOT NULL AND
                    sc.extracted_data IS NOT NULL AND
                    er.extracted_json IS NOT NULL
                LIMIT %s
            """
            self.cursor.execute(query, (limit,))
            rows = self.cursor.fetchall()

            # Get column names
            columns = [desc[0] for desc in self.cursor.description]

            # Convert rows to JSON-compatible format
            data = []
            for row in rows:
                row_dict = {}
                all_non_null = True
                for col_name, value in zip(columns, row):
                    if col_name in ('product_data', 'extracted_data'):  # JSONB fields
                        row_dict[col_name] = value or {}  # Convert None to empty dict
                        if not value:
                            all_non_null = False
                    elif col_name in ('markdown_content', 'enriched_data', 'extracted_json'):  # Text/character varying
                        row_dict[col_name] = value  # Preserve None, empty string, or text
                        if value is None:
                            all_non_null = False
                    elif col_name == 'id':
                        row_dict[col_name] = value  # Store id
                if all_non_null:
                    data.append(row_dict)
                    logger.debug(f"Row included: {json.dumps(row_dict, indent=2, ensure_ascii=False)}")
                else:
                    logger.debug(f"Row excluded (contains null field): {json.dumps(row_dict, indent=2, ensure_ascii=False)}")

            logger.info(f"Successfully fetched {len(rows)} rows, filtered to {len(data)} rows with all non-null fields.")
            return data

        except Exception as e:
            logger.error(f"Error fetching data from scrape_content: {str(e)}")
            raise

    def save_to_json(self, data: List[Dict[str, Any]], output_file: str) -> None:
        """
        Save all fetched data to a JSON file and print the first 5 entries.

        Args:
            data (List[Dict[str, Any]]): Data to save.
            output_file (str): Path to the output JSON file.
        """
        try:
            # Save all data to JSON file
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            logger.info(f"Saved {len(data)} entries to {output_file}.")

            # Print first 5 entries
            if data:
                logger.info("Printing first 5 entries from the dataset:")
                for i, entry in enumerate(data[:5], 1):
                    logger.info(f"\nEntry {i}:\n{json.dumps(entry, indent=2, ensure_ascii=False)}\n")
                    logger.info("-" * 80)
            else:
                logger.warning("No data to print.")

        except Exception as e:
            logger.error(f"Error saving or printing data: {str(e)}")
            raise

    def extract_image_urls(self, product_data: Dict[str, Any], markdown_content: str) -> List[str]:
        """
        Extract image URLs from product_data and markdown_content.

        Args:
            product_data (Dict[str, Any]): Product data JSON.
            markdown_content (str): Markdown content string.

        Returns:
            List[str]: List of image URLs.
        """
        image_urls = []
        # From product_data
        if product_data.get('image_urls'):
            image_urls.extend(product_data['image_urls'])
        elif product_data.get('images'):
            image_urls.extend([img['url'] for img in product_data['images'] if img.get('url')])

        # From markdown_content (e.g., ![alt](url))
        if markdown_content:
            markdown_images = re.findall(r'!\[.*?\]\((https?://[^\s)]+)\)', markdown_content)
            image_urls.extend(markdown_images)

        # Deduplicate and filter valid URLs
        image_urls = list(set([url for url in image_urls if url.startswith('http')]))
        logger.debug(f"Extracted image URLs: {image_urls}")
        return image_urls

    def create_prompt(self, row: Dict[str, Any]) -> Dict[str, str]:
        """
        Create a prompt-response pair for a single row of scrape_content data.
        The prompt incorporates product data, enriched data, and image URLs.
        The response is derived from extracted_json.

        Args:
            row (Dict[str, Any]): A single row from the scrape_content table with extracted_json.

        Returns:
            Dict[str, str]: A dictionary containing the prompt and response, or None if invalid.
        """
        try:
            # Extract fields
            product_data = row.get('product_data', {})
            markdown_content = row.get('markdown_content', '')
            enriched_data = row.get('enriched_data', '')
            extracted_json = row.get('extracted_json', '')

            # Log raw values
            logger.debug(f"Processing row for prompt creation (id: {row.get('id')}):")
            logger.debug(f"Raw product_data: {json.dumps(product_data, indent=2, ensure_ascii=False)}")
            logger.debug(f"Raw markdown_content: {repr(markdown_content)}")
            logger.debug(f"Raw enriched_data: {repr(enriched_data)}")
            logger.debug(f"Raw extracted_data: {json.dumps(row.get('extracted_data', {}), indent=2, ensure_ascii=False)}")
            logger.debug(f"Raw extracted_json: {repr(extracted_json)}")

            # Parse extracted_json
            try:
                extracted_json_data = json.loads(extracted_json) if extracted_json else {}
                logger.debug(f"Parsed extracted_json: {json.dumps(extracted_json_data, indent=2, ensure_ascii=False)}")
            except json.JSONDecodeError as e:
                logger.error(f"Invalid JSON in extracted_json for id {row.get('id')}: {str(e)}")
                return None

            # Handle enriched_data for display
            enriched_display = enriched_data if enriched_data else 'Empty'

            # Download images for VLM training
            logger.info(f"Downloading images for product id {row.get('id')}")
            downloaded_images = self.extract_and_download_images(product_data, markdown_content)

            # Clean markdown content by removing image URLs
            cleaned_markdown = self.clean_markdown_content(markdown_content)
            logger.debug(f"Cleaned markdown content: {len(markdown_content)} → {len(cleaned_markdown)} chars")

            # Generate system prompt using the comprehensive schema
            system_prompt = self.generate_system_prompt()

            # Create user prompt without images (images will be separate)
            user_prompt_parts = [
                "Product details:",
                "$PRODUCT_DATA",
                json.dumps(product_data, indent=2, ensure_ascii=False),
                "",
                "Enriched data:",
                "$ENRICHED_DATA",
                enriched_display,
                "",
                "Markdown content:",
                cleaned_markdown if cleaned_markdown else 'No markdown content available'
            ]

            user_prompt = "\n".join(user_prompt_parts)

            # Use the raw extracted_json as the response (no processing)
            if not extracted_json or extracted_json.strip() == '':
                logger.warning(f"No extracted_json data for id {row.get('id')}; skipping row.")
                return None

            # Use the raw extracted_json string as the response
            response_json_str = extracted_json

            logger.debug(f"Generated prompt-response pair for id {row.get('id')}:")
            logger.debug(f"System prompt length: {len(system_prompt)} chars")
            logger.debug(f"User prompt length: {len(user_prompt)} chars")
            logger.debug(f"Response length: {len(response_json_str)} chars")
            logger.debug(f"Images included: {len(downloaded_images)}")
            logger.debug(f"Response preview: {response_json_str[:500]}...")

            # Return in the new structured format
            return {
                "SystemPrompt": system_prompt,
                "UserPrompt": user_prompt,
                "Images": downloaded_images,
                "Response": response_json_str
            }

        except Exception as e:
            logger.error(f"Error creating prompt for row id {row.get('id')}: {str(e)}")
            return None

    def generate_prompts(self, data: List[Dict[str, Any]], output_file: str) -> List[Dict[str, Any]]:
        """
        Generate prompt-response pairs from the fetched data, save to a JSONL file, and print up to 3 sample pairs.

        Args:
            data (List[Dict[str, Any]]): Fetched data from scrape_content.
            output_file (str): Path to the output JSONL file for prompts.
        """
        try:
            prompts = []
            for row in data:
                prompt_data = self.create_prompt(row)
                if prompt_data:
                    prompts.append(prompt_data)
                    logger.info(f"Saved prompt-response pair for id {row.get('id')} to {output_file}.")

            # Save prompts to JSONL file with formatted structure
            with open(output_file, 'w', encoding='utf-8') as f:
                for prompt in prompts:
                    # Create a formatted JSON string with line gaps between sections
                    formatted_json = self.format_json_with_gaps(prompt)
                    f.write(formatted_json + '\n')

            logger.info(f"Generated and saved {len(prompts)} prompt-response pairs to {output_file}.")

            # Print up to 3 sample prompts (with truncated content for readability)
            logger.info("Printing up to 3 sample prompt-response pairs:")
            for i, prompt_data in enumerate(prompts[:3], 1):
                logger.info(f"\nSample Pair {i}:\n")
                # Truncate system prompt for readability
                system_preview = prompt_data['SystemPrompt'][:500] + "..." if len(prompt_data['SystemPrompt']) > 500 else prompt_data['SystemPrompt']
                logger.info(f"System Prompt (first 500 chars):\n{system_preview}\n")

                # Truncate user prompt for readability
                user_preview = prompt_data['UserPrompt'][:500] + "..." if len(prompt_data['UserPrompt']) > 500 else prompt_data['UserPrompt']
                logger.info(f"User Prompt (first 500 chars):\n{user_preview}\n")

                logger.info(f"Images included: {len(prompt_data.get('Images', []))}")
                logger.info(f"Response length: {len(prompt_data['Response'])} chars")
                logger.info("-" * 80)

            return prompts

        except Exception as e:
            logger.error(f"Error generating prompts: {str(e)}")
            raise

def main():
    # Database configuration
    db_config = {
        "host": "c-fsg-ai-scrape.cxtat3pbomo5js.postgres.cosmos.azure.com",
        "database": "citus",
        "user": "citus",
        "password": "2xFsVX8idSjiCSs",
        "port": 5432
    }

    # Output file paths
    data_output_file = "scrape_content_data.json"
    prompts_output_file = "scrape_content_prompts.jsonl"

    # Initialize data loader with schema file
    data_loader = ScrapeContentDataLoader(db_config, schema_file="response_schema.json")

    try:
        # Connect to the database
        data_loader.connect()

        # Fetch up to 50 rows from scrape_content
        data = data_loader.fetch_scrape_content(limit=50)

        # Save all entries to JSON and print first 5
        data_loader.save_to_json(data, data_output_file)

        # Generate and save prompt-response pairs
        prompts = data_loader.generate_prompts(data, prompts_output_file)

        # Generate schema coverage statistics
        data_loader.get_schema_statistics(data)

        # Generate image download statistics
        if prompts:
            total_images = sum(len(prompt.get('Images', [])) for prompt in prompts)
            records_with_images = sum(1 for prompt in prompts if len(prompt.get('Images', [])) > 0)
            logger.info(f"Image Download Statistics:")
            logger.info(f"Total images downloaded: {total_images}")
            logger.info(f"Records with images: {records_with_images}/{len(prompts)} ({records_with_images/len(prompts)*100:.1f}%)")
            logger.info(f"Average images per record: {total_images/len(prompts):.1f}")

    except Exception as e:
        logger.error(f"Pipeline failed: {str(e)}")
        raise
    finally:
        # Ensure connection is closed
        data_loader.close_connection()

if __name__ == "__main__":
    main()