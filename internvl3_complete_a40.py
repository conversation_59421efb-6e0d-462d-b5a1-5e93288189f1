#!/usr/bin/env python3
"""
Complete InternVL3-8B Inference for A40 GPU
Based on official example code with A40 optimizations
"""

import json
import time
import math
import numpy as np
import torch
import torchvision.transforms as T
from datetime import datetime
from io import BytesIO
from PIL import Image
from torchvision.transforms.functional import InterpolationMode
from transformers import AutoModel, AutoTokenizer, AutoConfig
import base64
import re
import jsonschema

# Import existing utilities
from utils.data_loader import load_prompts_from_jsonl

# Constants from official example
IMAGENET_MEAN = (0.485, 0.456, 0.406)
IMAGENET_STD = (0.229, 0.224, 0.225)

def build_transform(input_size):
    """Build image transformation pipeline"""
    MEAN, STD = IMAGENET_MEAN, IMAGENET_STD
    transform = T.Compose([
        T.Lambda(lambda img: img.convert('RGB') if img.mode != 'RGB' else img),
        T.Resize((input_size, input_size), interpolation=InterpolationMode.BICUBIC),
        T<PERSON>(),
        T.Normalize(mean=MEAN, std=STD)
    ])
    return transform

def find_closest_aspect_ratio(aspect_ratio, target_ratios, width, height, image_size):
    """Find the closest aspect ratio from target ratios"""
    best_ratio_diff = float('inf')
    best_ratio = (1, 1)
    area = width * height
    for ratio in target_ratios:
        target_aspect_ratio = ratio[0] / ratio[1]
        ratio_diff = abs(aspect_ratio - target_aspect_ratio)
        if ratio_diff < best_ratio_diff:
            best_ratio_diff = ratio_diff
            best_ratio = ratio
        elif ratio_diff == best_ratio_diff:
            if area > 0.5 * image_size * image_size * ratio[0] * ratio[1]:
                best_ratio = ratio
    return best_ratio

def dynamic_preprocess(image, min_num=1, max_num=12, image_size=448, use_thumbnail=False):
    """Dynamic preprocessing for InternVL3"""
    orig_width, orig_height = image.size
    aspect_ratio = orig_width / orig_height

    # Calculate target ratios
    target_ratios = set(
        (i, j) for n in range(min_num, max_num + 1)
        for i in range(1, n + 1)
        for j in range(1, n + 1)
        if i * j <= max_num and i * j >= min_num
    )
    target_ratios = sorted(target_ratios, key=lambda x: x[0] * x[1])

    # Find the closest aspect ratio
    target_aspect_ratio = find_closest_aspect_ratio(
        aspect_ratio, target_ratios, orig_width, orig_height, image_size
    )

    # Calculate target dimensions
    target_width = image_size * target_aspect_ratio[0]
    target_height = image_size * target_aspect_ratio[1]
    blocks = target_aspect_ratio[0] * target_aspect_ratio[1]

    # Resize the image
    resized_img = image.resize((target_width, target_height))
    processed_images = []

    for i in range(blocks):
        box = (
            (i % (target_width // image_size)) * image_size,
            (i // (target_width // image_size)) * image_size,
            ((i % (target_width // image_size)) + 1) * image_size,
            ((i // (target_width // image_size)) + 1) * image_size
        )
        # Split the image
        split_img = resized_img.crop(box)
        processed_images.append(split_img)

    assert len(processed_images) == blocks

    if use_thumbnail and len(processed_images) != 1:
        thumbnail_img = image.resize((image_size, image_size))
        processed_images.append(thumbnail_img)

    return processed_images

def load_image_from_base64(base64_data, input_size=448, max_num=6):
    """Load image from base64 data using official preprocessing"""
    try:
        if base64_data.startswith('data:'):
            header, base64_content = base64_data.split(',', 1)
        else:
            base64_content = base64_data.strip()

        image_bytes = base64.b64decode(base64_content)
        image = Image.open(BytesIO(image_bytes)).convert('RGB')

        # Apply official InternVL3 preprocessing
        transform = build_transform(input_size=input_size)
        images = dynamic_preprocess(
            image,
            image_size=input_size,
            use_thumbnail=True,
            max_num=max_num
        )
        pixel_values = [transform(img) for img in images]
        pixel_values = torch.stack(pixel_values)

        print(f"    📏 Processed into {len(images)} tiles: {pixel_values.shape}")
        return pixel_values

    except Exception as e:
        print(f"    ❌ Image processing error: {e}")
        return None

def split_model_a40():
    """Create device map optimized for single A40 GPU"""
    # For A40 with 48GB, we can use auto device mapping
    return "auto"

def load_response_schema():
    """Load the response schema for validation"""
    try:
        with open('response_schema.json', 'r') as f:
            schema = json.load(f)
        return schema
    except Exception as e:
        print(f"⚠️ Could not load response schema: {e}")
        return None

def validate_json_response(response_text, schema=None):
    """Validate if response is valid JSON and follows schema"""
    try:
        # Try to parse as JSON
        parsed_json = json.loads(response_text)

        # Validate against schema if provided
        if schema:
            jsonschema.validate(parsed_json, schema)
            return parsed_json, True, "Valid JSON following schema"
        else:
            return parsed_json, True, "Valid JSON (schema not available)"

    except json.JSONDecodeError as e:
        return None, False, f"Invalid JSON: {e}"
    except jsonschema.ValidationError as e:
        return None, False, f"Schema validation failed: {e.message}"
    except Exception as e:
        return None, False, f"Validation error: {e}"

def extract_json_from_response(response_text):
    """Extract JSON from response text that might contain extra text"""
    # Try to find JSON block in the response
    json_patterns = [
        r'\{.*\}',  # Simple JSON object
        r'\[.*\]',  # JSON array
    ]

    for pattern in json_patterns:
        matches = re.findall(pattern, response_text, re.DOTALL)
        if matches:
            # Try the largest match (most likely to be complete JSON)
            largest_match = max(matches, key=len)
            try:
                parsed = json.loads(largest_match)
                return largest_match, parsed
            except:
                continue

    return None, None

def remove_urls_from_text(text: str):
    """Remove URLs from text to reduce token count"""
    url_patterns = [
        r'https?://[^\s<>"]+',
        r'www\.[^\s<>"]+',
        r'ftp://[^\s<>"]+',
        r'[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(?:/[^\s<>"]*)?',
    ]

    cleaned_text = text
    urls_removed = 0

    for pattern in url_patterns:
        matches = re.findall(pattern, cleaned_text)
        urls_removed += len(matches)
        cleaned_text = re.sub(pattern, '', cleaned_text)

    # Clean up whitespace
    cleaned_text = re.sub(r'\s+', ' ', cleaned_text)
    cleaned_text = re.sub(r'\n\s*\n\s*\n', '\n\n', cleaned_text)
    cleaned_text = cleaned_text.strip()

    return cleaned_text, urls_removed

def main():
    """Complete InternVL3-8B inference for A40 GPU"""
    print("🌟 InternVL3-8B Complete Inference for A40 GPU")
    print("=" * 60)
    print("🎯 Based on official example with A40 optimizations")

    # A40 configuration
    model_path = 'OpenGVLab/InternVL3-8B'
    print(f"💾 Target GPU: A40 (48GB VRAM)")
    print(f"🔢 Using 8-bit quantization for A40")

    # Load response schema for validation (optional - schema is in system prompt)
    print("📋 Loading response schema for validation...")
    response_schema = load_response_schema()
    if response_schema:
        print("✅ Response schema loaded for validation")
        print("📋 Note: Schema is already included in system prompts (like Phi-3-Vision)")
    else:
        print("⚠️ Response schema file not found - will validate JSON format only")
        print("📋 Schema is included in system prompts (like Phi-3-Vision)")

    # Load prompts
    input_files = ['final_image_prompts_cleaned.jsonl', 'scrape_content_prompts.jsonl']
    prompts = None

    for input_file in input_files:
        prompts = load_prompts_from_jsonl(input_file)
        if prompts:
            print(f"✅ Using prompts from: {input_file}")
            break

    if not prompts:
        print("❌ No prompts found")
        return

    # Initialize model using official approach
    print(f"\n🤖 INITIALIZING INTERNVL3-8B FOR A40")
    print("-" * 50)

    try:
        # Use A40-optimized device mapping
        device_map = split_model_a40()

        print("🔄 Loading tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(
            model_path,
            trust_remote_code=True,
            use_fast=False
        )
        print("✅ Tokenizer loaded successfully")

        print("🔄 Loading model with A40-optimized 8-bit settings...")
        model = AutoModel.from_pretrained(
            model_path,
            torch_dtype=torch.bfloat16,  # Base dtype
            load_in_8bit=True,           # 8-bit quantization for A40
            low_cpu_mem_usage=True,      # Memory optimization
            use_flash_attn=True,         # A40 supports flash attention
            trust_remote_code=True,      # Required for InternVL3
            device_map=device_map        # A40 device mapping
        ).eval()

        print("✅ Model loaded successfully for A40")
        print(f"💾 Model device: {next(model.parameters()).device}")
        print(f"🔢 Model dtype: {next(model.parameters()).dtype}")

        # Check A40 memory usage
        if torch.cuda.is_available():
            memory_allocated = torch.cuda.memory_allocated() / 1024**3
            memory_reserved = torch.cuda.memory_reserved() / 1024**3
            print(f"📊 A40 Memory - Allocated: {memory_allocated:.1f}GB, Reserved: {memory_reserved:.1f}GB")
            print(f"📊 A40 Usage: {memory_reserved/48*100:.1f}% of 48GB")

    except Exception as e:
        print(f"❌ Model initialization failed: {e}")
        print("💡 A40 troubleshooting:")
        print("  1. Install: pip install bitsandbytes flash-attn")
        print("  2. Check A40 CUDA drivers")
        print("  3. Ensure sufficient VRAM")
        return

    # Generation config matching Phi-3-Vision style (temperature 0.3)
    generation_config = {
        'max_new_tokens': 2000,  # Reasonable output length (like Phi-3-Vision)
        'temperature': 0.3,      # Same as Phi-3-Vision for consistency
        'do_sample': True,
        'top_p': 0.9,
        'repetition_penalty': 1.05
    }

    print(f"🎛️ Generation config: {generation_config}")

    # Process prompts
    results = []
    max_prompts = min(len(prompts), 5)

    print(f"\n🚀 PROCESSING {max_prompts} PROMPTS")
    print("=" * 60)

    for i, prompt_data in enumerate(prompts[:max_prompts], 1):
        print(f"\n{'='*15} PROMPT {i}/{max_prompts} {'='*15}")

        try:
            # Extract data
            system_prompt = prompt_data.get('SystemPrompt', '')
            user_prompt = prompt_data.get('UserPrompt', '')
            image_data_urls = prompt_data.get('Images', [])

            print(f"📋 Original lengths:")
            print(f"  System: {len(system_prompt):,} chars")
            print(f"  User: {len(user_prompt):,} chars")
            print(f"  Images: {len(image_data_urls)}")

            # Remove URLs
            cleaned_system, system_urls = remove_urls_from_text(system_prompt)
            cleaned_user, user_urls = remove_urls_from_text(user_prompt)

            total_urls = system_urls + user_urls
            total_saved = (len(system_prompt) - len(cleaned_system)) + (len(user_prompt) - len(cleaned_user))

            print(f"🔗 URL removal results:")
            print(f"  URLs removed: {total_urls}")
            print(f"  Characters saved: {total_saved:,}")

            # Process images using official method
            processed_images = []
            print(f"🖼️ Processing images with official InternVL3 method...")

            for j, img_data in enumerate(image_data_urls[:3]):  # Limit to 3 images for A40
                pixel_values = load_image_from_base64(img_data, input_size=448, max_num=6)
                if pixel_values is not None:
                    processed_images.append(pixel_values)
                    print(f"  ✓ Image {j+1}: {pixel_values.shape}")
                else:
                    print(f"  ✗ Image {j+1}: failed")

            if not processed_images:
                print("❌ No valid images, skipping prompt")
                continue

            # Combine images using official approach
            if len(processed_images) == 1:
                combined_pixel_values = processed_images[0]
                num_patches_list = [processed_images[0].size(0)]
            else:
                combined_pixel_values = torch.cat(processed_images, dim=0)
                num_patches_list = [img.size(0) for img in processed_images]

            # Move to A40 GPU
            combined_pixel_values = combined_pixel_values.to(torch.bfloat16).cuda()

            print(f"📊 Combined images: {combined_pixel_values.shape}")
            print(f"📊 Patches per image: {num_patches_list}")

            # Create question using EXACT Phi-3-Vision style prompt structure
            # System prompt contains the complete schema (just like Phi-3-Vision)
            # Model follows schema naturally from system prompt (no extra enforcement needed)

            if len(processed_images) == 1:
                # Single image: <image>\n{system_with_schema}\n\n{user_question}
                question = f"<image>\n{cleaned_system}\n\n{cleaned_user}"
            else:
                # Multi-image format: Image-1: <image>\nImage-2: <image>\n{system_with_schema}\n\n{user_question}
                image_prefix = "".join([f"Image-{j+1}: <image>\n" for j in range(len(processed_images))])
                question = f"{image_prefix}{cleaned_system}\n\n{cleaned_user}"

            print(f"📝 Question length: {len(question):,} chars")
            print(f"📋 Schema source: System prompt contains complete schema (like Phi-3-Vision)")
            print(f"📋 Prompt structure: Exact Phi-3-Vision style (system+user combined)")

            # Run inference using official chat method
            print("🚀 Running InternVL3 inference on A40...")
            start_time = time.time()

            if len(processed_images) == 1:
                # Single image inference
                response = model.chat(
                    tokenizer,
                    combined_pixel_values,
                    question,
                    generation_config
                )
            else:
                # Multi-image inference with num_patches_list
                response = model.chat(
                    tokenizer,
                    combined_pixel_values,
                    question,
                    generation_config,
                    num_patches_list=num_patches_list
                )

            inference_time = time.time() - start_time

            print(f"✅ A40 inference completed in {inference_time:.2f}s")
            print(f"📝 Generated: {len(response):,} chars")
            print(f"📄 Preview: {response[:300]}...")

            # Validate response format (schema is in system prompt, like Phi-3-Vision)
            print("🔍 Validating response format...")
            print("📋 Schema source: System prompt (following Phi-3-Vision approach)")

            parsed_json, is_valid, validation_message = validate_json_response(response, response_schema)

            if not is_valid:
                print(f"⚠️ Response validation failed: {validation_message}")
                # Try to extract JSON from response (in case model adds extra text)
                extracted_json_text, extracted_json = extract_json_from_response(response)
                if extracted_json:
                    print("🔧 Extracted JSON from response")
                    parsed_json, is_valid, validation_message = validate_json_response(extracted_json_text, response_schema)
                    if is_valid:
                        response = extracted_json_text  # Use extracted JSON
                        print("✅ Extracted JSON is valid")
                    else:
                        print(f"❌ Extracted JSON still invalid: {validation_message}")
                else:
                    print("❌ Could not extract valid JSON from response")
                    print("💡 Model should follow schema from system prompt naturally")
            else:
                print(f"✅ Response validation: {validation_message}")
                print("✅ Model followed schema from system prompt correctly")

            # Store results with schema validation info
            result = {
                'prompt_id': f"prompt_{i}",
                'model': "OpenGVLab/InternVL3-8B",
                'gpu': "A40",
                'precision': "8bit",
                'original_system_length': len(system_prompt),
                'original_user_length': len(user_prompt),
                'cleaned_system_length': len(cleaned_system),
                'cleaned_user_length': len(cleaned_user),
                'urls_removed': total_urls,
                'chars_saved': total_saved,
                'num_images': len(processed_images),
                'num_patches_list': num_patches_list,
                'total_patches': sum(num_patches_list),
                'generated_response': response,
                'generated_length': len(response),
                'inference_time': inference_time,
                'generation_config': generation_config,
                'schema_validation': {
                    'is_valid_json': is_valid,
                    'validation_message': validation_message,
                    'parsed_json': parsed_json if is_valid else None,
                    'schema_available': response_schema is not None
                },
                'success': True
            }

            results.append(result)

        except Exception as e:
            print(f"❌ Error processing prompt {i}: {e}")
            results.append({
                'prompt_id': f"prompt_{i}",
                'success': False,
                'error': str(e)
            })

    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f'internvl3_a40_results_{timestamp}.json'

    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)

    # Summary
    successful = [r for r in results if r.get('success', False)]

    print(f"\n📊 A40 SUMMARY:")
    print("=" * 60)
    print(f"🤖 Model: InternVL3-8B (Phi-3-Vision style prompts)")
    print(f"💾 GPU: A40 (48GB)")
    print(f"🔢 Precision: 8-bit")
    print(f"📋 Schema source: System prompts (like Phi-3-Vision)")
    print(f"🌡️ Temperature: 0.3 (matching Phi-3-Vision)")
    print(f"📊 Processed: {len(results)} prompts")
    print(f"✅ Successful: {len(successful)}")

    if successful:
        avg_time = sum(r['inference_time'] for r in successful) / len(successful)
        total_urls = sum(r['urls_removed'] for r in successful)
        total_saved = sum(r['chars_saved'] for r in successful)
        avg_length = sum(r['generated_length'] for r in successful) / len(successful)
        avg_patches = sum(r['total_patches'] for r in successful) / len(successful)

        # Schema validation statistics
        valid_json_count = sum(1 for r in successful if r['schema_validation']['is_valid_json'])
        schema_compliance_rate = (valid_json_count / len(successful)) * 100

        print(f"⏱️ Average time: {avg_time:.2f}s")
        print(f"🔗 Total URLs removed: {total_urls:,}")
        print(f"💾 Total chars saved: {total_saved:,}")
        print(f"📝 Average response: {avg_length:,.0f} chars")
        print(f"🖼️ Average patches: {avg_patches:.1f}")
        print(f"📋 Schema compliance: {valid_json_count}/{len(successful)} ({schema_compliance_rate:.1f}%)")

        # A40 memory efficiency
        if torch.cuda.is_available():
            final_memory = torch.cuda.memory_reserved() / 1024**3
            print(f"📊 Final A40 usage: {final_memory:.1f}GB ({final_memory/48*100:.1f}%)")

    print(f"\n💾 Results saved to: {results_file}")
    print("🎉 A40 InternVL3-8B inference completed!")

if __name__ == "__main__":
    main()
