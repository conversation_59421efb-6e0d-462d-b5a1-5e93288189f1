#!/usr/bin/env python3
"""
Safe test script for InternVL3-8B model
Enhanced with CUDA error handling and dtype safety
"""

import torch
import os
import warnings
import requests
from PIL import Image
from io import BytesIO

# Set environment variables for CUDA debugging
os.environ["CUDA_LAUNCH_BLOCKING"] = "1"
os.environ["TORCH_USE_CUDA_DSA"] = "1"

# Suppress warnings
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=FutureWarning)

from utils.internvl_model_initializer import initialize_internvl3, create_generation_config
from utils.internvl_image_processor import load_image_from_path, prepare_image_from_base64

def safe_cuda_operation(func, *args, **kwargs):
    """Safely execute CUDA operations with error handling"""
    try:
        torch.cuda.empty_cache()  # Clear cache before operation
        result = func(*args, **kwargs)
        torch.cuda.synchronize()  # Ensure operation completes
        return result, None
    except Exception as e:
        torch.cuda.empty_cache()  # Clear cache on error
        return None, str(e)

def download_test_image(url, save_path=None):
    """Download a test image safely"""
    try:
        response = requests.get(url, timeout=30)
        image = Image.open(BytesIO(response.content)).convert('RGB')
        if save_path:
            image.save(save_path)
        return image
    except Exception as e:
        print(f"❌ Error downloading image: {e}")
        return None

def test_internvl3_safe():
    """Test InternVL3-8B optimized for A40 GPU"""
    print("🧪 A40-Optimized InternVL3-8B Testing")
    print("=" * 60)
    print("🛡️ A40 GPU: 8-bit precision, dtype consistency, error handling")

    # A40-specific configuration
    print("💾 Target GPU: A40 (48GB VRAM)")
    print("🔢 Using 8-bit quantization for A40 optimization")
    print("🔢 Image dtype will match model dtype automatically")

    # Check CUDA availability
    if not torch.cuda.is_available():
        print("❌ CUDA not available. This model requires GPU.")
        return

    print(f"✅ CUDA available: {torch.cuda.get_device_name()}")
    print(f"💾 GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")

    # Initialize model with error handling
    print("\n🤖 INITIALIZING MODEL")
    print("-" * 40)

    try:
        # Clear GPU memory before initialization
        torch.cuda.empty_cache()

        # Use A40-optimized initialization
        model, tokenizer, model_dtype = initialize_internvl3(precision="8bit")
        print("✅ Model initialized successfully for A40")

        # Check model device and dtype
        model_device = next(model.parameters()).device
        print(f"💾 Model device: {model_device}")
        print(f"🔢 Model dtype: {model_dtype}")

    except Exception as e:
        print(f"❌ Model initialization failed: {e}")
        print("\n🔧 A40 Troubleshooting:")
        print("1. Install: pip install bitsandbytes accelerate flash-attn")
        print("2. Check A40 CUDA drivers")
        print("3. Ensure 48GB VRAM available")
        print("4. Try: export CUDA_VISIBLE_DEVICES=0")
        return

    # Create generation config
    generation_config = create_generation_config(max_tokens=100, temperature=0.1)  # Conservative settings
    print(f"🎛️ Generation config: {generation_config}")

    # Test 1: Pure text conversation (safest test)
    print("\n🧪 TEST 1: Pure Text Conversation")
    print("-" * 40)

    try:
        question = "Hello, please introduce yourself in one sentence."
        print(f"Question: {question}")

        # Use safe CUDA operation wrapper
        response, error = safe_cuda_operation(
            model.chat,
            tokenizer,
            None,
            question,
            generation_config
        )

        if error:
            print(f"❌ Text conversation failed: {error}")
            print("💡 This might be a model compatibility issue")
        else:
            print(f"Response: {response}")
            print("✅ Text conversation test successful!")

    except Exception as e:
        print(f"❌ Text conversation test failed: {e}")
        print("💡 Try running with CUDA_LAUNCH_BLOCKING=1 for more details")

    # Test 2: Simple image test (if text worked)
    print("\n🧪 TEST 2: Simple Image Test")
    print("-" * 40)

    # Download test image
    test_image_url = "https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/bee.jpg"
    print(f"📥 Downloading test image...")

    test_image = download_test_image(test_image_url, "test_image_safe.jpg")

    if test_image:
        print(f"✅ Test image downloaded: {test_image.size}")

        try:
            # Process image with A40-optimized settings
            pixel_values = load_image_from_path(
                "test_image_safe.jpg",
                max_num=6,  # A40 can handle more tiles
                model_dtype=model_dtype  # Use model's dtype
            )

            if pixel_values is not None:
                print(f"📊 Image processed: {pixel_values.shape}")
                print(f"🔢 Image dtype: {pixel_values.dtype}")

                # Move to GPU safely
                device = next(model.parameters()).device

                # Use safe CUDA operation for tensor movement
                def move_to_device():
                    return pixel_values.to(device)

                pixel_values_gpu, error = safe_cuda_operation(move_to_device)

                if error:
                    print(f"❌ Failed to move image to GPU: {error}")
                    return

                # Test image description with safe operation
                question = "<image>\nDescribe this image briefly."
                print(f"Question: {question}")

                response, error = safe_cuda_operation(
                    model.chat,
                    tokenizer,
                    pixel_values_gpu,
                    question,
                    generation_config
                )

                if error:
                    print(f"❌ Image description failed: {error}")
                    print("💡 This might be a dtype mismatch or memory issue")
                else:
                    print(f"Response: {response}")
                    print("✅ Image description test successful!")

            else:
                print("❌ Image processing failed")

        except Exception as e:
            print(f"❌ Image test failed: {e}")
            print("💡 Try reducing image size or using different precision")

    # Memory cleanup
    print("\n🧹 CLEANUP")
    print("-" * 40)

    try:
        torch.cuda.empty_cache()
        print("✅ GPU memory cleared")
    except Exception as e:
        print(f"⚠️ Cleanup warning: {e}")

    print("\n🎉 Safe testing completed!")
    print("\nℹ️ If tests passed, you can run:")
    print("  python internvl3_inference.py")
    print("\nℹ️ If tests failed, try:")
    print("  1. Different precision settings")
    print("  2. Smaller batch sizes")
    print("  3. Check GPU memory availability")

if __name__ == "__main__":
    test_internvl3_safe()
