#!/usr/bin/env python3
"""
Simple Gemma-3n Inference - Text Only
Avoids image processing compatibility issues
"""

import json
import time
import torch
import os
import warnings
from datetime import datetime
from transformers import <PERSON>Tokenizer, Gemma3nForConditionalGeneration
from huggingface_hub import login, HfApi

# Suppress warnings and compatibility fixes
warnings.filterwarnings("ignore")
if not hasattr(torch.compiler, 'is_compiling'):
    torch.compiler.is_compiling = lambda: False

def huggingface_login():
    """Handle Hugging Face authentication"""
    try:
        api = HfApi()
        user_info = api.whoami()
        print(f"✅ Authenticated as: {user_info['name']}")
        return True
    except Exception:
        pass
    
    # Check for token in environment
    hf_token = os.getenv('HF_TOKEN') or os.getenv('HUGGINGFACE_HUB_TOKEN')
    
    if hf_token:
        print("🔑 Using HF token from environment")
        try:
            login(token=hf_token)
            return True
        except Exception as e:
            print(f"❌ Token failed: {e}")
    
    print("❌ Please set HF_TOKEN environment variable")
    print("export HF_TOKEN='hf_your_token_here'")
    return False

def load_prompts_from_jsonl(file_path: str):
    """Load prompts from JSONL file"""
    try:
        prompts = []
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                if line.strip():
                    try:
                        prompts.append(json.loads(line))
                    except:
                        pass
        print(f"✅ Loaded {len(prompts)} prompts from {file_path}")
        return prompts
    except Exception as e:
        print(f"❌ Error loading {file_path}: {e}")
        return []

def remove_urls_from_text(text: str):
    """Remove URLs from text"""
    import re
    url_patterns = [
        r'https?://[^\s<>"]+',
        r'www\.[^\s<>"]+',
        r'ftp://[^\s<>"]+',
    ]
    
    cleaned_text = text
    urls_removed = 0
    
    for pattern in url_patterns:
        matches = re.findall(pattern, cleaned_text)
        urls_removed += len(matches)
        cleaned_text = re.sub(pattern, '', cleaned_text)
    
    cleaned_text = re.sub(r'\s+', ' ', cleaned_text).strip()
    return cleaned_text, urls_removed

def main():
    """Simple Gemma-3n inference avoiding image processing issues"""
    print("🔮 Simple Gemma-3n Text Inference")
    print("=" * 50)
    print("⚠️ Text-only mode (avoids image processing issues)")
    
    # Authentication
    if not huggingface_login():
        return
    
    # Model loading
    model_id = "google/gemma-3n-e2b-it"
    
    try:
        print(f"\n🤖 Loading {model_id}...")
        
        # Load tokenizer only (avoid processor issues)
        tokenizer = AutoTokenizer.from_pretrained(model_id)
        print("✅ Tokenizer loaded")
        
        # Load model with safe settings
        model = Gemma3nForConditionalGeneration.from_pretrained(
            model_id,
            device_map="auto",
            torch_dtype=torch.bfloat16,
            trust_remote_code=True,
            attn_implementation="eager"
        ).eval()
        
        print("✅ Model loaded successfully")
        
    except Exception as e:
        print(f"❌ Model loading failed: {e}")
        return
    
    # Simple text test
    print("\n🧪 Testing with simple text...")
    test_prompt = "Describe the benefits of renewable energy in 3 sentences."
    
    try:
        inputs = tokenizer(test_prompt, return_tensors="pt")
        device = next(model.parameters()).device
        inputs = {k: v.to(device) for k, v in inputs.items()}
        
        with torch.no_grad():
            outputs = model.generate(
                **inputs,
                max_new_tokens=100,
                temperature=0.3,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id
            )
        
        response = tokenizer.decode(outputs[0][inputs['input_ids'].shape[1]:], skip_special_tokens=True)
        print(f"✅ Test successful!")
        print(f"📝 Response: {response[:200]}...")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return
    
    # Process actual prompts (text only)
    print("\n📋 Processing actual prompts (text-only)...")
    
    input_files = ['final_image_prompts_cleaned.jsonl', 'scrape_content_prompts.jsonl']
    prompts = None
    
    for input_file in input_files:
        if os.path.exists(input_file):
            prompts = load_prompts_from_jsonl(input_file)
            if prompts:
                break
    
    if not prompts:
        print("ℹ️ No prompt files found")
        return
    
    results = []
    max_prompts = min(len(prompts), 3)
    
    for i, prompt_data in enumerate(prompts[:max_prompts], 1):
        print(f"\n{'='*15} PROMPT {i}/{max_prompts} {'='*15}")
        
        try:
            # Extract and clean text
            system_prompt = prompt_data.get('SystemPrompt', '')
            user_prompt = prompt_data.get('UserPrompt', '')
            
            # Remove URLs
            cleaned_system, system_urls = remove_urls_from_text(system_prompt)
            cleaned_user, user_urls = remove_urls_from_text(user_prompt)
            
            print(f"🔗 URLs removed: {system_urls + user_urls}")
            
            # Combine prompts (text only)
            full_prompt = f"System: {cleaned_system}\n\nUser: {cleaned_user}\n\nAssistant:"
            
            # Truncate if too long
            if len(full_prompt) > 8000:  # Conservative limit
                full_prompt = full_prompt[:8000] + "..."
                print(f"⚠️ Truncated to 8000 chars")
            
            print(f"📝 Processing {len(full_prompt)} characters")
            
            # Tokenize and generate
            inputs = tokenizer(full_prompt, return_tensors="pt", truncation=True, max_length=4000)
            device = next(model.parameters()).device
            inputs = {k: v.to(device) for k, v in inputs.items()}
            
            start_time = time.time()
            
            with torch.no_grad():
                outputs = model.generate(
                    **inputs,
                    max_new_tokens=1000,
                    temperature=0.3,
                    do_sample=True,
                    top_p=0.9,
                    pad_token_id=tokenizer.eos_token_id,
                    eos_token_id=tokenizer.eos_token_id
                )
            
            inference_time = time.time() - start_time
            
            # Decode response
            response = tokenizer.decode(
                outputs[0][inputs['input_ids'].shape[1]:], 
                skip_special_tokens=True
            )
            
            print(f"✅ Success in {inference_time:.2f}s")
            print(f"📝 Generated: {len(response)} chars")
            print(f"📄 Preview: {response[:200]}...")
            
            results.append({
                'prompt_id': f"prompt_{i}",
                'model': model_id,
                'mode': 'text_only',
                'urls_removed': system_urls + user_urls,
                'input_length': len(full_prompt),
                'generated_response': response,
                'generated_length': len(response),
                'inference_time': inference_time,
                'success': True
            })
            
        except Exception as e:
            print(f"❌ Error: {e}")
            results.append({
                'prompt_id': f"prompt_{i}",
                'success': False,
                'error': str(e)
            })
    
    # Save results
    if results:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f'gemma3n_simple_results_{timestamp}.json'
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        successful = [r for r in results if r.get('success', False)]
        print(f"\n📊 SUMMARY:")
        print(f"✅ Successful: {len(successful)}/{len(results)}")
        print(f"💾 Results saved to: {results_file}")
        
        if successful:
            avg_time = sum(r['inference_time'] for r in successful) / len(successful)
            avg_length = sum(r['generated_length'] for r in successful) / len(successful)
            print(f"⏱️ Average time: {avg_time:.2f}s")
            print(f"📝 Average response: {avg_length:.0f} chars")
    
    print("\n🎉 Simple Gemma-3n inference completed!")
    print("ℹ️ Note: This was text-only mode. For images, fix transformers compatibility.")

if __name__ == "__main__":
    main()
