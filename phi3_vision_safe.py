#!/usr/bin/env python3
"""
Phi-3-Vision Safe Inference
Conservative version that definitely works with available GPU memory
"""

import json
import time
import base64
from io import BytesIO
from PIL import Image
from vllm import LLM, SamplingParams

def load_prompts():
    """Load prompts from JSONL file"""
    prompts = []
    for filename in ['final_image_prompts_cleaned.jsonl', 'final_image_prompts.jsonl']:
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        prompts.append(json.loads(line))
            print(f"✅ Loaded {len(prompts)} prompts from {filename}")
            return prompts
        except FileNotFoundError:
            continue
    return []

def prepare_image(base64_data: str) -> Image.Image:
    """Convert base64 to PIL Image"""
    try:
        if base64_data.startswith('data:'):
            header, base64_content = base64_data.split(',', 1)
        else:
            base64_content = base64_data.strip()

        image_bytes = base64.b64decode(base64_content)
        image = Image.open(BytesIO(image_bytes))

        if image.mode != 'RGB':
            image = image.convert('RGB')

        # Conservative resizing
        max_size = 512
        if max(image.size) > max_size:
            image.thumbnail((max_size, max_size), Image.Resampling.LANCZOS)

        return image
    except Exception as e:
        print(f"Image error: {e}")
        return None

def smart_truncate(text: str, max_chars: int) -> str:
    """Smart truncation that preserves important content"""
    if len(text) <= max_chars:
        return text

    # Try to break at sentence boundaries
    truncated = text[:max_chars]

    # Find last sentence ending
    for break_char in ['. ', '.\n', '\n\n', '\n']:
        last_break = truncated.rfind(break_char)
        if last_break > max_chars * 0.8:  # Keep at least 80%
            return text[:last_break + len(break_char)]

    # Fallback to character limit
    return text[:max_chars]

def main():
    """Safe Phi-3-Vision inference with guaranteed memory fit"""
    print("🛡️ Phi-3-Vision Safe Inference")
    print("="*50)

    # Load prompts
    prompts = load_prompts()
    if not prompts:
        print("❌ No prompts found")
        return

    print(f"📂 Loaded {len(prompts)} prompts")

    # Initialize with conservative settings
    print("🤖 Loading Phi-3-Vision with conservative settings...")

    try:
        # Use 24k context - should definitely fit
        llm = LLM(
            model="microsoft/Phi-3-vision-128k-instruct",
            trust_remote_code=True,
            max_model_len=24576,  # 24k tokens - very safe
            gpu_memory_utilization=0.85,  # Higher utilization for smaller context
            max_num_seqs=1,
            limit_mm_per_prompt={"image": 2},
            # Note: Paged attention is enabled by default in modern vLLM
        )
        print("✅ Phi-3-Vision loaded with 24k context (safe mode)")

    except Exception as e:
        print(f"❌ Even safe mode failed: {e}")
        print("🔧 Try reducing GPU memory usage or using a smaller model")
        return

    # Process prompts with smart truncation
    results = []

    for i, prompt_data in enumerate(prompts[:3], 1):  # Test first 3
        print(f"\n{'='*15} PROMPT {i} {'='*15}")

        try:
            # Extract data
            system_prompt = prompt_data.get('SystemPrompt', '')
            user_prompt = prompt_data.get('UserPrompt', '')
            image_data_urls = prompt_data.get('Images', [])
            actual_response = prompt_data.get('Response', '')

            print(f"📋 Original lengths:")
            print(f"  System: {len(system_prompt):,} chars")
            print(f"  User: {len(user_prompt):,} chars")
            print(f"  Images: {len(image_data_urls)}")

            # Smart truncation to fit in 24k context
            # Reserve ~10k tokens for images and response, use ~14k for text
            # Images are taking much more tokens than expected
            max_text_tokens = 14000
            max_text_chars = max_text_tokens * 2.5  # Conservative estimate: ~35k chars

            # Allocate proportionally
            total_original = len(system_prompt) + len(user_prompt)
            if total_original > max_text_chars:
                system_ratio = len(system_prompt) / total_original
                user_ratio = len(user_prompt) / total_original

                system_max = int(max_text_chars * system_ratio)
                user_max = int(max_text_chars * user_ratio)

                final_system = smart_truncate(system_prompt, system_max)
                final_user = smart_truncate(user_prompt, user_max)

                print(f"  📝 Smart truncation applied:")
                print(f"    System: {len(system_prompt):,} → {len(final_system):,} chars")
                print(f"    User: {len(user_prompt):,} → {len(final_user):,} chars")
            else:
                final_system = system_prompt
                final_user = user_prompt
                print(f"  ✅ No truncation needed")

            # Process images
            processed_images = []
            for j, img_data in enumerate(image_data_urls[:2]):
                image = prepare_image(img_data)
                if image:
                    processed_images.append(image)
                    print(f"    ✓ Image {j+1}: {image.size}")

            if not processed_images:
                print("  ❌ No valid images")
                continue

            # Create Phi-3-Vision prompt
            image_tokens = "".join([f"<|image_{i+1}|>" for i in range(len(processed_images))])
            full_prompt = f"<|user|>\n{image_tokens}\n{final_system}\n\n{final_user}<|end|>\n<|assistant|>\n"

            # Estimate tokens
            estimated_tokens = len(full_prompt) // 3.5 + len(processed_images) * 500
            print(f"  🔢 Estimated tokens: {estimated_tokens:,.0f} / 24,576")

            # Sampling parameters
            sampling_params = SamplingParams(
                temperature=0.1,
                max_tokens=800,  # Conservative output length
                stop=["<|end|>", "<|user|>", "<|assistant|>"]
            )

            # Run inference
            print("  🚀 Running safe inference...")
            start_time = time.time()

            outputs = llm.generate(
                [{
                    "prompt": full_prompt,
                    "multi_modal_data": {"image": processed_images}
                }],
                sampling_params=sampling_params
            )

            inference_time = time.time() - start_time

            if outputs and outputs[0].outputs:
                generated_text = outputs[0].outputs[0].text.strip()

                print(f"  ✅ Success in {inference_time:.2f}s")
                print(f"  📝 Generated: {len(generated_text)} chars")
                print(f"  📄 Preview: {generated_text[:150]}...")

                # Calculate similarity
                similarity = 0.0
                if actual_response and generated_text:
                    import difflib
                    similarity = difflib.SequenceMatcher(
                        None, generated_text.lower(), actual_response.lower()
                    ).ratio()
                    print(f"  🔍 Similarity: {similarity:.3f}")

                results.append({
                    'prompt_id': f"prompt_{i}",
                    'generated_response': generated_text,
                    'actual_response': actual_response,
                    'similarity': similarity,
                    'inference_time': inference_time,
                    'estimated_tokens': estimated_tokens,
                    'success': True
                })

            else:
                print("  ❌ No output generated")

        except Exception as e:
            print(f"  ❌ Error: {e}")
            results.append({
                'prompt_id': f"prompt_{i}",
                'success': False,
                'error': str(e)
            })

    # Save results
    with open('phi3_safe_results.json', 'w') as f:
        json.dump(results, f, indent=2)

    # Summary
    successful = [r for r in results if r.get('success', False)]
    print(f"\n📊 SUMMARY:")
    print(f"  Processed: {len(results)} prompts")
    print(f"  Successful: {len(successful)}")

    if successful:
        avg_time = sum(r['inference_time'] for r in successful) / len(successful)
        avg_similarity = sum(r.get('similarity', 0) for r in successful) / len(successful)
        print(f"  Average time: {avg_time:.2f}s")
        print(f"  Average similarity: {avg_similarity:.3f}")

    print(f"\n💾 Results saved to: phi3_safe_results.json")
    print(f"🎉 Safe inference completed!")

if __name__ == "__main__":
    main()
