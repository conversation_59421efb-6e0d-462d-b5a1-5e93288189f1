#!/usr/bin/env python3
"""
Simple test script for InternVL3-8B model
Tests the basic functionality with sample images
"""

import torch
import requests
from PIL import Image
from io import BytesIO
from utils.internvl_model_initializer import initialize_internvl3, create_generation_config
from utils.internvl_image_processor import load_image_from_path, prepare_image_from_base64

def download_test_image(url, save_path=None):
    """Download a test image"""
    try:
        response = requests.get(url)
        image = Image.open(BytesIO(response.content)).convert('RGB')
        if save_path:
            image.save(save_path)
        return image
    except Exception as e:
        print(f"❌ Error downloading image: {e}")
        return None

def test_internvl3():
    """Test InternVL3-8B with simple examples"""
    print("🧪 Testing InternVL3-8B Model")
    print("=" * 50)
    
    # Initialize model
    try:
        model, tokenizer = initialize_internvl3()
        print("✅ Model initialized successfully")
    except Exception as e:
        print(f"❌ Model initialization failed: {e}")
        return
    
    # Create generation config
    generation_config = create_generation_config(max_tokens=200, temperature=0.3)
    print(f"🎛️ Generation config: {generation_config}")
    
    # Test 1: Pure text conversation
    print("\n🧪 TEST 1: Pure Text Conversation")
    print("-" * 40)
    
    try:
        question = "Hello, who are you? Please introduce yourself briefly."
        print(f"Question: {question}")
        
        response = model.chat(tokenizer, None, question, generation_config)
        print(f"Response: {response}")
        print("✅ Text conversation test successful!")
        
    except Exception as e:
        print(f"❌ Text conversation test failed: {e}")
    
    # Test 2: Download and test with image
    print("\n🧪 TEST 2: Single Image Description")
    print("-" * 40)
    
    # Download test image
    test_image_url = "https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/bee.jpg"
    print(f"📥 Downloading test image from: {test_image_url}")
    
    test_image = download_test_image(test_image_url, "test_image.jpg")
    
    if test_image:
        print(f"✅ Test image downloaded: {test_image.size}")
        
        try:
            # Process image for InternVL3
            from utils.internvl_image_processor import load_image_from_path
            pixel_values = load_image_from_path("test_image.jpg", max_num=6)
            
            if pixel_values is not None:
                # Move to GPU
                device = next(model.parameters()).device
                pixel_values = pixel_values.to(torch.bfloat16).to(device)
                
                print(f"📊 Image processed: {pixel_values.shape}")
                
                # Test image description
                question = "<image>\nPlease describe this image in detail."
                print(f"Question: {question}")
                
                response = model.chat(tokenizer, pixel_values, question, generation_config)
                print(f"Response: {response}")
                print("✅ Image description test successful!")
                
                # Test follow-up question
                print("\n🔄 Follow-up question:")
                question2 = "What colors do you see in this image?"
                print(f"Question: {question2}")
                
                response2, history = model.chat(
                    tokenizer, 
                    pixel_values, 
                    question2, 
                    generation_config, 
                    history=None, 
                    return_history=True
                )
                print(f"Response: {response2}")
                print("✅ Follow-up question test successful!")
                
            else:
                print("❌ Image processing failed")
                
        except Exception as e:
            print(f"❌ Image description test failed: {e}")
    
    # Test 3: Multi-round conversation
    print("\n🧪 TEST 3: Multi-round Conversation")
    print("-" * 40)
    
    try:
        # First question
        question1 = "Can you tell me about renewable energy?"
        print(f"Question 1: {question1}")
        
        response1, history = model.chat(
            tokenizer, 
            None, 
            question1, 
            generation_config, 
            history=None, 
            return_history=True
        )
        print(f"Response 1: {response1}")
        
        # Follow-up question
        question2 = "What are the main types of renewable energy?"
        print(f"Question 2: {question2}")
        
        response2, history = model.chat(
            tokenizer, 
            None, 
            question2, 
            generation_config, 
            history=history, 
            return_history=True
        )
        print(f"Response 2: {response2}")
        print("✅ Multi-round conversation test successful!")
        
    except Exception as e:
        print(f"❌ Multi-round conversation test failed: {e}")
    
    print("\n🎉 InternVL3-8B testing completed!")
    print("\nℹ️ If all tests passed, you can run:")
    print("  python internvl3_inference.py")

if __name__ == "__main__":
    test_internvl3()
