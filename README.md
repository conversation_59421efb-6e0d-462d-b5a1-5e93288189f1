# Amazon Product Data VLM Pipeline

## 🎯 **Complete End-to-End Pipeline**

This project creates a complete pipeline from Amazon product data scraping to VLM fine-tuning. It extracts product information from PostgreSQL databases, downloads product images, generates structured training data, and provides multiple VLM inference options for product analysis.

### **Pipeline Overview:**
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐    ┌──────────────────┐
│   PostgreSQL    │    │  Data Processing │    │  Training Data  │    │  VLM Inference   │
│   Database      │───▶│                  │───▶│                 │───▶│                  │
│                 │    │  - Data Fetching │    │  - JSONL Format │    │  - 6 VLM Scripts │
│ - scrape_content│    │  - Image Download│    │  - Base64 Images│    │  - Auto-truncate │
│ - extraction_   │    │  - Prompt Gen.   │    │  - Structured   │    │  - 9 Models      │
│   results       │    │  - Schema Valid. │    │    Responses    │    │  - 16k+ Responses│
└─────────────────┘    └──────────────────┘    └─────────────────┘    └──────────────────┘
```

## 📁 **Complete Directory Structure**

### 🎯 **Data Processing Scripts**
- **`scape_data_to_prompt.py`** - Main pipeline script that processes PostgreSQL data into VLM training format

### 🤖 **VLM Inference Scripts (6 Working Options)**
- **`universal_vlm_inference.py`** - 🌟 **RECOMMENDED!** Universal VLM script with 9 models + auto-truncation + URL removal
- **`phi3_vision_robust.py`** - 🛡️ **BULLETPROOF!** Guaranteed to work with any prompt length + full 128k context
- **`phi3_vision_long_response.py`** - Optimized for very long responses (up to 24k chars)
- **`phi3_vision_safe.py`** - Conservative 24k context with smart truncation
- **`phi3_vision_chunked.py`** - Processes long prompts in chunks for complete coverage
- **`vllm_vlm_inference.py`** - Advanced multi-model inference with comparison features + URL removal

### 📊 **Data Files**
- **`scrape_content_data.json`** - Raw extracted data from PostgreSQL database
- **`scrape_content_prompts.jsonl`** - Generated VLM training data (main output)
- **`final_image_prompts_cleaned.jsonl`** - Clean prompt-response pairs for VLM training
- **`response_schema.json`** - JSON schema defining the expected response structure

### 📚 **Documentation**
- **`RESPONSE_LENGTH_GUIDE.md`** - Guide for generating 16k+ character responses
- **`MARKDOWN_CLEANING_UPDATE.md`** - Documentation on markdown cleaning features
- **`README.md`** - This file

### ⚙️ **Configuration**
- **`requirements.txt`** - Python dependencies

## 🚀 **Quick Start**

### **1. Install Dependencies**
```bash
pip install -r requirements.txt
# Main dependencies: psycopg2-binary, requests, pillow, vllm, transformers
```

### **2. Database Setup**
Configure your PostgreSQL connection in `scape_data_to_prompt.py`:
```python
db_config = {
    "host": "your-postgres-host",
    "database": "your-database",
    "user": "your-username",
    "password": "your-password",
    "port": 5432
}
```

**Required Database Tables:**
- **`scrape_content`** - Contains product data, markdown content, enriched data
- **`extraction_results`** - Contains structured response data

### **3. Generate VLM Training Data**
```bash
python scape_data_to_prompt.py
# Processes PostgreSQL data → scrape_content_prompts.jsonl
# Downloads product images and converts to base64
# Creates structured prompt-response pairs
```

### **4. Run VLM Inference**

**🌟 RECOMMENDED - Universal VLM with auto-truncation:**
```bash
python universal_vlm_inference.py
# Interactive menu to choose from 9 VLM models
# Automatic truncation prevents token errors
# No more "decoder prompt too long" errors!
```

**🛡️ BULLETPROOF - For extremely long prompts:**
```bash
python phi3_vision_robust.py
# Guaranteed to work with any prompt length
# Progressive context fallback + aggressive truncation
```

**🔒 SAFE MODE - Conservative approach:**
```bash
python phi3_vision_safe.py
# 24k context with smart truncation
# Preserves sentence boundaries
```

**🧩 CHUNKED PROCESSING - Complete coverage:**
```bash
python phi3_vision_chunked.py
# Processes prompts in chunks
# Combines results for comprehensive analysis
```

**📝 LONG RESPONSES - Detailed analysis:**
```bash
python phi3_vision_long_response.py
# Up to 24k character responses
```

**🔧 GENERAL INFERENCE - Standard processing:**
```bash
python vllm_vlm_inference.py
# Multi-model support with comparison features
```

## 🗃️ **Database Schema & Data Processing**

### **Required PostgreSQL Tables:**

#### **1. `scrape_content` Table:**
```sql
CREATE TABLE scrape_content (
    id SERIAL PRIMARY KEY,
    product_data JSONB,           -- Product information (GTIN, brand, etc.)
    markdown_content TEXT,        -- Product descriptions and details
    enriched_data JSONB,         -- Additional enriched product data
    images_url TEXT[],           -- Array of product image URLs
    created_at TIMESTAMP DEFAULT NOW()
);
```

#### **2. `extraction_results` Table:**
```sql
CREATE TABLE extraction_results (
    id SERIAL PRIMARY KEY,
    scrape_content_id INTEGER REFERENCES scrape_content(id),
    response_data JSONB,         -- Structured response following schema
    created_at TIMESTAMP DEFAULT NOW()
);
```

### **Data Processing Pipeline:**

#### **Step 1: Data Extraction**
```python
# scape_data_to_prompt.py extracts:
- Product data (JSONB) → System/User prompts
- Image URLs → Downloaded and converted to base64
- Markdown content → Cleaned (image URLs removed)
- Enriched data → Additional context
- Response data → Expected structured output
```

#### **Step 2: Image Processing**
```python
# Automatic image processing:
- Downloads images from URLs in product_data.images_url
- Converts to base64 data URLs
- Resizes for efficiency (max 768px)
- Removes image URLs from markdown content
- Stores base64 images in "Images" field
```

#### **Step 3: Prompt Generation**
```python
# Creates structured prompts:
SystemPrompt: Comprehensive schema + instructions
UserPrompt: Product details + enriched data + markdown
Images: Base64 encoded product images
Response: Expected structured JSON output
```

### **Output Format (JSONL):**
```json
{
  "SystemPrompt": "RESPONSE SCHEMA:\n{detailed_schema}\n\nINSTRUCTIONS:\n{instructions}",
  "UserPrompt": "Product details:\n{product_data}\n\nEnriched data:\n{enriched_data}\n\nMarkdown content:\n{cleaned_markdown}",
  "Images": ["data:image/jpeg;base64,/9j/4AAQ...", "data:image/png;base64,iVBOR..."],
  "Response": "{structured_json_response}"
}
```

## 📊 **Response Length Capabilities**

| Script | Max Tokens | Expected Characters | Best For |
|--------|------------|-------------------|----------|
| `phi3_vision_long_response.py` | 6,000 | ~24,000 | Very detailed analysis |
| `universal_vlm_inference.py` | 4,096 | ~16,400 | Model selection + auto-truncation |
| `phi3_vision_safe.py` | 800 | ~3,200 | Conservative with smart truncation |
| `phi3_vision_chunked.py` | 500/chunk | Multiple chunks | Complete prompt coverage |
| `phi3_vision_robust.py` | 800 | ~3,200 | Guaranteed success with any prompt |
| `vllm_vlm_inference.py` | 4,096 | ~16,400 | Standard analysis |

## 🎯 **Key Features**

### **Data Processing:**
- ✅ **Automatic image URL removal** from markdown content
- ✅ **Base64 image preservation** for VLM training
- ✅ **Comprehensive schema coverage** (100% of expected fields)
- ✅ **Clean JSONL format** for easy processing

### **VLM Inference:**
- ✅ **🔗 NEW: Automatic URL removal** from prompts (saves 15k+ tokens)
- ✅ **Full 128k context utilization** for Phi-3-Vision (was limited to 40k)
- ✅ **16k+ character responses** supported
- ✅ **Multiple VLM models** (Phi-3-Vision, LLaVA, Qwen2-VL)
- ✅ **Progressive context fallback** (128k → 120k → 100k → 80k)
- ✅ **Response comparison** with expected outputs
- ✅ **Automatic similarity scoring**

### **Quality Assurance:**
- ✅ **Perfect prompt structure** (SystemPrompt, UserPrompt, Images, Response)
- ✅ **Valid JSON responses** with complete schema coverage
- ✅ **High-quality images** (4-5 per record, base64 encoded)
- ✅ **Rich product data** (100k+ character prompts)

## 📈 **Data Quality & Schema**

### **Generated Training Data Quality:**
- **47 high-quality records** from PostgreSQL database
- **100% schema coverage** in all responses
- **4-5 images per record** (base64 encoded, cleaned URLs from markdown)
- **100k+ character prompts** (system + user combined)
- **9k-12k character responses** (detailed structured JSON)

### **Product Coverage:**
- **Multiple categories**: Grocery, international foods, confectionery, beverages
- **Complete nutritional data**: Macronutrients, micronutrients, allergen information
- **Detailed ingredient analysis**: Including nested ingredients and sub-components
- **Comprehensive claims**: Certifications, nutritional claims, preparation instructions
- **Clean label attributes**: Preservatives, artificial ingredients, processing methods

### **Response Schema Structure:**
```json
{
  "analysed_data": {
    "generalData": {
      "gtin": "Product GTIN/barcode",
      "brand": "Brand name",
      "productName": "Product name",
      "category": "Product category",
      "subCategory": "Sub-category"
    },
    "servingSize": {
      "size": "Serving size amount",
      "unit": "Serving unit",
      "servingsPerPack": "Number of servings"
    },
    "nutritionalInformation": {
      "statedNutrition": "Per 100g nutrition facts",
      "qualifiedNutrition": "Per serving nutrition facts"
    },
    "ingredients": {
      "ingredientList": "Complete ingredient list",
      "nestedIngredients": "Sub-ingredients breakdown"
    },
    "claims": {
      "certifications": "Organic, Fair Trade, etc.",
      "nutritionalClaims": "Low fat, high protein, etc.",
      "preparationInstructions": "Cooking/usage instructions"
    },
    "NPI 2.0 Food Packages - Allergens & Intolerances": {
      "statedAllergens": "Explicitly stated allergens",
      "qualifiedAllergens": "May contain allergens"
    },
    "cleanLabel": {
      "artificialIngredients": "Artificial colors, flavors, etc.",
      "preservatives": "Chemical preservatives used",
      "processingMethods": "How the product is processed"
    }
  }
}
```

## 🎉 **Ready for Production**

This cleaned directory contains everything needed for:
- ✅ **VLM fine-tuning** on product analysis
- ✅ **Long-form response generation** (16k+ characters)
- ✅ **Structured data extraction** from product images
- ✅ **Multi-modal AI training** (text + images)

## 💡 **Usage Examples**

### **Generate Training Data:**
```bash
# Process product data into VLM training format
python scape_data_to_prompt.py

# Output: scrape_content_prompts.jsonl (47 records)
```

### **Run Inference:**
```bash
# Generate 16k+ character product analysis
python phi3_vision_long_response.py

# Expected output:
# 📝 Generated: 18,247 characters
# 🎯 Achievement: 114.0% of 16k target
# 🏆 16k+ characters: ✅ ACHIEVED
```

### **Compare Responses:**
```bash
# Run inference with similarity comparison
python vllm_vlm_inference.py

# Shows similarity scores vs expected responses
```

## 🔧 **System Requirements**

### **For Data Processing:**
- **PostgreSQL**: Database with scrape_content and extraction_results tables
- **Python**: 3.8+ with psycopg2-binary, requests, pillow
- **Storage**: ~500MB for generated JSONL files
- **Network**: Internet access for image downloading

### **For VLM Inference:**
- **GPU**: 20GB+ VRAM (for Phi-3-Vision), 6GB+ (for Qwen2-VL-2B)
- **RAM**: 32GB+ recommended
- **Python**: 3.8+ with vLLM, transformers
- **CUDA**: Compatible GPU for vLLM

## 🛠️ **Troubleshooting**

### **Database Connection Issues:**
```bash
# Test PostgreSQL connection
python -c "import psycopg2; print('PostgreSQL connection OK')"

# Check table existence
psql -h your-host -d your-db -c "\dt scrape_content extraction_results"
```

### **Image Download Issues:**
```bash
# Check internet connectivity and image URLs
# Script automatically handles failed downloads
# Check logs for specific image URL errors
```

### **VLM Inference Issues:**
```bash
# For "decoder prompt too long" errors:
python phi3_vision_robust.py  # Guaranteed to work

# For GPU memory issues:
python universal_vlm_inference.py  # Select smaller model

# For installation issues:
pip install --upgrade vllm transformers torch
```

## 🎯 **Advanced Usage**

### **Custom Database Configuration:**
```python
# Modify scape_data_to_prompt.py for custom schemas
class CustomDataLoader(ScrapeContentDataLoader):
    def load_data_from_db(self):
        # Custom SQL queries for different table structures
        pass
```

### **Custom Response Schema:**
```python
# Modify response_schema.json for different output formats
# Update system prompt generation in scape_data_to_prompt.py
```

### **Batch Processing:**
```python
# Process large datasets in batches
python scape_data_to_prompt.py --batch-size 100 --start-id 1000
```

