#!/usr/bin/env python3
"""
Image processing utilities for VLM inference
"""

import base64
from io import BytesIO
from PIL import Image

def prepare_image_conservative(base64_data: str) -> Image.Image:
    """Convert base64 to PIL Image with conservative resizing"""
    try:
        if base64_data.startswith('data:'):
            header, base64_content = base64_data.split(',', 1)
        else:
            base64_content = base64_data.strip()

        image_bytes = base64.b64decode(base64_content)
        image = Image.open(BytesIO(image_bytes))

        if image.mode != 'RGB':
            image = image.convert('RGB')

        # Conservative resizing to minimize token usage
        max_size = 384
        if max(image.size) > max_size:
            original_size = image.size
            image.thumbnail((max_size, max_size), Image.Resampling.LANCZOS)
            print(f"    📏 Resized: {original_size} → {image.size}")

        return image
    except Exception as e:
        print(f"    ❌ Image error: {e}")
        return None
