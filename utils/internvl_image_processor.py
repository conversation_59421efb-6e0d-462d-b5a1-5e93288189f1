#!/usr/bin/env python3
"""
InternVL3-8B image processing utilities
"""

import math
import base64
import torch
import torchvision.transforms as T
from io import BytesIO
from PIL import Image
from torchvision.transforms.functional import InterpolationMode

IMAGENET_MEAN = (0.485, 0.456, 0.406)
IMAGENET_STD = (0.229, 0.224, 0.225)

def build_transform(input_size=448):
    """Build image transformation pipeline"""
    MEAN, STD = IMAGENET_MEAN, IMAGENET_STD
    transform = T.Compose([
        T.Lambda(lambda img: img.convert('RGB') if img.mode != 'RGB' else img),
        T.Resize((input_size, input_size), interpolation=InterpolationMode.BICUBIC),
        T.<PERSON>(),
        T.Normalize(mean=MEAN, std=STD)
    ])
    return transform

def find_closest_aspect_ratio(aspect_ratio, target_ratios, width, height, image_size):
    """Find the closest aspect ratio from target ratios"""
    best_ratio_diff = float('inf')
    best_ratio = (1, 1)
    area = width * height

    for ratio in target_ratios:
        target_aspect_ratio = ratio[0] / ratio[1]
        ratio_diff = abs(aspect_ratio - target_aspect_ratio)
        if ratio_diff < best_ratio_diff:
            best_ratio_diff = ratio_diff
            best_ratio = ratio
        elif ratio_diff == best_ratio_diff:
            if area > 0.5 * image_size * image_size * ratio[0] * ratio[1]:
                best_ratio = ratio
    return best_ratio

def dynamic_preprocess(image, min_num=1, max_num=12, image_size=448, use_thumbnail=False):
    """Dynamic preprocessing for InternVL3"""
    orig_width, orig_height = image.size
    aspect_ratio = orig_width / orig_height

    # Calculate target ratios
    target_ratios = set(
        (i, j) for n in range(min_num, max_num + 1)
        for i in range(1, n + 1)
        for j in range(1, n + 1)
        if i * j <= max_num and i * j >= min_num
    )
    target_ratios = sorted(target_ratios, key=lambda x: x[0] * x[1])

    # Find the closest aspect ratio
    target_aspect_ratio = find_closest_aspect_ratio(
        aspect_ratio, target_ratios, orig_width, orig_height, image_size
    )

    # Calculate target dimensions
    target_width = image_size * target_aspect_ratio[0]
    target_height = image_size * target_aspect_ratio[1]
    blocks = target_aspect_ratio[0] * target_aspect_ratio[1]

    # Resize the image
    resized_img = image.resize((target_width, target_height))
    processed_images = []

    for i in range(blocks):
        box = (
            (i % (target_width // image_size)) * image_size,
            (i // (target_width // image_size)) * image_size,
            ((i % (target_width // image_size)) + 1) * image_size,
            ((i // (target_width // image_size)) + 1) * image_size
        )
        # Split the image
        split_img = resized_img.crop(box)
        processed_images.append(split_img)

    assert len(processed_images) == blocks

    if use_thumbnail and len(processed_images) != 1:
        thumbnail_img = image.resize((image_size, image_size))
        processed_images.append(thumbnail_img)

    return processed_images

def prepare_image_from_base64(base64_data: str, input_size=448, max_num=12, precision="float16"):
    """Convert base64 to processed tensor for InternVL3 with configurable precision"""
    try:
        if base64_data.startswith('data:'):
            header, base64_content = base64_data.split(',', 1)
        else:
            base64_content = base64_data.strip()

        image_bytes = base64.b64decode(base64_content)
        image = Image.open(BytesIO(image_bytes)).convert('RGB')

        # Apply InternVL3 preprocessing
        transform = build_transform(input_size=input_size)
        images = dynamic_preprocess(
            image,
            image_size=input_size,
            use_thumbnail=True,
            max_num=max_num
        )
        pixel_values = [transform(img) for img in images]
        pixel_values = torch.stack(pixel_values)

        # Convert to specified precision for memory efficiency
        if precision == "float16":
            pixel_values = pixel_values.to(torch.float16)
        elif precision == "bfloat16":
            pixel_values = pixel_values.to(torch.bfloat16)
        # Default is float32

        print(f"    📏 Processed into {len(images)} tiles: {pixel_values.shape}")
        print(f"    🔢 Image tensor dtype: {pixel_values.dtype}")
        return pixel_values

    except Exception as e:
        print(f"    ❌ Image processing error: {e}")
        return None

def load_image_from_path(image_path: str, input_size=448, max_num=12, precision="float16"):
    """Load and process image from file path with configurable precision"""
    try:
        image = Image.open(image_path).convert('RGB')
        transform = build_transform(input_size=input_size)
        images = dynamic_preprocess(
            image,
            image_size=input_size,
            use_thumbnail=True,
            max_num=max_num
        )
        pixel_values = [transform(img) for img in images]
        pixel_values = torch.stack(pixel_values)

        # Convert to specified precision
        if precision == "float16":
            pixel_values = pixel_values.to(torch.float16)
        elif precision == "bfloat16":
            pixel_values = pixel_values.to(torch.bfloat16)

        return pixel_values
    except Exception as e:
        print(f"❌ Error loading image from {image_path}: {e}")
        return None

def combine_images(image_tensors):
    """Combine multiple image tensors for multi-image inference"""
    if not image_tensors:
        return None, []

    # Calculate number of patches for each image
    num_patches_list = [tensor.size(0) for tensor in image_tensors]

    # Concatenate all images
    combined_tensor = torch.cat(image_tensors, dim=0)

    return combined_tensor, num_patches_list

def create_image_prompt(num_images, mode="combined"):
    """Create image prompt tokens for InternVL3"""
    if mode == "combined":
        # For combined images: single <image> token
        return "<image>\n"
    elif mode == "separate":
        # For separate images: Image-1: <image>\nImage-2: <image>\n...
        return "".join([f"Image-{i+1}: <image>\n" for i in range(num_images)])
    else:
        # Default: just <image> tokens
        return "<image>\n" * num_images
