#!/usr/bin/env python3
"""
Model initialization utilities for VLM inference
"""

from vllm import LLM

def initialize_phi3_with_fallback():
    """Initialize Phi-3-Vision with progressive fallback - larger context for no truncation"""

    # Larger context attempts to accommodate full prompts without truncation
    # Your prompt needs ~60k tokens, so starting higher
    context_attempts = [
        {"context": 80000, "gpu_util": 0.75, "description": "80k context (for full prompts)"},
        {"context": 70000, "gpu_util": 0.8, "description": "70k context"},
        {"context": 65000, "gpu_util": 0.85, "description": "65k context"},
        {"context": 60000, "gpu_util": 0.9, "description": "60k context"},
        {"context": 55000, "gpu_util": 0.85, "description": "55k context"},
        {"context": 50000, "gpu_util": 0.8, "description": "50k context"},
        {"context": 45000, "gpu_util": 0.75, "description": "45k context"},
        {"context": 40000, "gpu_util": 0.7, "description": "40k context"},
        {"context": 35000, "gpu_util": 0.65, "description": "35k context"},
        {"context": 30000, "gpu_util": 0.6, "description": "30k context (fallback)"}
    ]

    for attempt in context_attempts:
        try:
            print(f"  🔄 Trying {attempt['description']}...")

            # Calculate estimated KV cache memory needed
            estimated_kv_memory = (attempt["context"] / 1000) * 0.9  # Rough estimate: ~0.9 GB per 1k tokens
            print(f"    📊 Estimated KV cache needed: ~{estimated_kv_memory:.1f} GiB")

            llm = LLM(
                model="microsoft/Phi-3-vision-128k-instruct",
                trust_remote_code=True,
                max_model_len=attempt["context"],
                gpu_memory_utilization=attempt["gpu_util"],
                swap_space=8,  # Increased swap for larger contexts
                max_num_seqs=1,
                limit_mm_per_prompt={"image": 2},  # Limit to 2 images
                enforce_eager=False,  # Allow torch.compile for efficiency
                # Note: Paged attention is enabled by default in modern vLLM
            )

            print(f"  ✅ Success with {attempt['description']}")
            print(f"    💾 GPU utilization: {attempt['gpu_util']*100:.0f}%")
            return llm, attempt["context"]

        except Exception as e:
            error_msg = str(e)
            print(f"  ❌ Failed: {error_msg[:150]}...")

            # Check if it's a memory error and provide specific guidance
            if "KV cache" in error_msg and "memory" in error_msg:
                print(f"    💡 Memory issue detected - trying smaller context...")
            elif "CUDA out of memory" in error_msg:
                print(f"    💡 GPU memory exhausted - reducing utilization...")

            continue

    raise Exception("Could not initialize Phi-3-Vision with any context size")
