#!/usr/bin/env python3
"""
Data loading utilities for VLM inference
"""

import json

def load_prompts_from_jsonl(file_path: str):
    """Load prompts from JSONL file"""
    print(f"📂 Loading prompts from {file_path}...")

    try:
        prompts = []
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Handle formatted JSON records
        brace_count = 0
        current_record = ""

        for line in content.split('\n'):
            if line.strip() == '{' and brace_count == 0:
                if current_record.strip():
                    try:
                        record = json.loads(current_record.strip())
                        prompts.append(record)
                    except json.JSONDecodeError:
                        pass
                current_record = line + '\n'
                brace_count = 1
            elif brace_count > 0:
                current_record += line + '\n'
                brace_count += line.count('{') - line.count('}')

                if brace_count == 0:
                    try:
                        record = json.loads(current_record.strip())
                        prompts.append(record)
                    except json.JSONDecodeError:
                        pass
                    current_record = ""

        if current_record.strip():
            try:
                record = json.loads(current_record.strip())
                prompts.append(record)
            except json.JSONDecodeError:
                pass

        print(f"✅ Loaded {len(prompts)} prompts")
        return prompts

    except Exception as e:
        print(f"❌ Error loading prompts: {str(e)}")
        return []
