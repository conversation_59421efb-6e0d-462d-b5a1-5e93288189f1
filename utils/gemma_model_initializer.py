#!/usr/bin/env python3
"""
Gemma-3n model initialization utilities
"""

import torch
import os
from transformers import AutoProcessor, Gemma3nForConditionalGeneration
from huggingface_hub import login, <PERSON>f<PERSON><PERSON>

def check_huggingface_auth():
    """Check if user is authenticated with Hugging Face"""
    try:
        api = HfApi()
        user_info = api.whoami()
        print(f"✅ Authenticated as: {user_info['name']}")
        return True
    except Exception:
        return False

def huggingface_login():
    """Handle Hugging Face authentication"""
    print("🔐 Hugging Face Authentication Required")
    print("=" * 50)

    # Check if already authenticated
    if check_huggingface_auth():
        print("✅ Already authenticated with Hugging Face")
        return True

    # Check for token in environment
    hf_token = os.getenv('HF_TOKEN') or os.getenv('HUGGINGFACE_HUB_TOKEN')

    if hf_token:
        print("🔑 Found HF token in environment variables")
        try:
            login(token=hf_token)
            if check_huggingface_auth():
                print("✅ Successfully authenticated with environment token")
                return True
        except Exception as e:
            print(f"❌ Environment token failed: {e}")

    # Interactive login
    print("\n📝 Interactive login required:")
    print("1. Go to https://huggingface.co/settings/tokens")
    print("2. Create a new token with 'Read' permissions")
    print("3. Copy the token and paste it below")
    print("4. Or set HF_TOKEN environment variable")

    try:
        # Try interactive login
        login()
        if check_huggingface_auth():
            print("✅ Successfully authenticated interactively")
            return True
    except Exception as e:
        print(f"❌ Interactive login failed: {e}")

    print("\n💡 Alternative: Set environment variable:")
    print("export HF_TOKEN='your_token_here'")
    print("or")
    print("export HUGGINGFACE_HUB_TOKEN='your_token_here'")

    return False

def initialize_gemma3n():
    """Initialize Gemma-3n model with optimal settings and authentication"""

    model_id = "google/gemma-3n-e2b-it"

    print(f"🤖 Initializing Gemma-3n model: {model_id}")

    # Handle authentication for gated model
    print("🔐 Checking Hugging Face authentication...")
    if not huggingface_login():
        raise Exception("Hugging Face authentication failed. Cannot access gated model.")

    print("📊 Loading model and processor...")

    try:
        # Initialize processor
        processor = AutoProcessor.from_pretrained(model_id)
        print("✅ Processor loaded successfully")

        # Initialize model with optimal settings
        model = Gemma3nForConditionalGeneration.from_pretrained(
            model_id,
            device_map="cuda",
            torch_dtype=torch.bfloat16,
            trust_remote_code=True
        ).eval()

        print("✅ Model loaded successfully")
        print(f"💾 Device: {model.device}")
        print(f"🔢 Dtype: {model.dtype}")

        return model, processor

    except Exception as e:
        print(f"❌ Error loading Gemma-3n model: {e}")

        # Provide helpful error messages
        if "gated repo" in str(e) or "401" in str(e):
            print("\n🚨 AUTHENTICATION ERROR:")
            print("1. The model is gated and requires approval")
            print("2. Visit: https://huggingface.co/google/gemma-3n-e2b-it")
            print("3. Request access to the model")
            print("4. Wait for approval (usually quick)")
            print("5. Make sure you're logged in with the approved account")

        raise e

def create_gemma_messages(system_prompt: str, user_prompt: str, images: list):
    """Create properly formatted messages for Gemma-3n"""

    messages = [
        {
            "role": "system",
            "content": [{"type": "text", "text": system_prompt}]
        }
    ]

    # Create user message with images and text
    user_content = []

    # Add images first
    for image in images:
        user_content.append({"type": "image", "image": image})

    # Add text prompt
    user_content.append({"type": "text", "text": user_prompt})

    messages.append({
        "role": "user",
        "content": user_content
    })

    return messages
