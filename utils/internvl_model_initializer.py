#!/usr/bin/env python3
"""
InternVL3-8B model initialization utilities
"""

import torch
import math
from transformers import <PERSON>Token<PERSON>, AutoModel, AutoConfig, BitsAndBytesConfig

def split_model_device_map(model_name="InternVL3-8B"):
    """Create device map for multi-GPU setup"""
    device_map = {}
    world_size = torch.cuda.device_count()

    if world_size == 1:
        # Single GPU setup
        return "auto"

    try:
        # Multi-GPU setup
        config = AutoConfig.from_pretrained("OpenGVLab/InternVL3-8B", trust_remote_code=True)
        num_layers = config.llm_config.num_hidden_layers

        # Since the first GPU will be used for ViT, treat it as half a GPU
        num_layers_per_gpu = math.ceil(num_layers / (world_size - 0.5))
        num_layers_per_gpu = [num_layers_per_gpu] * world_size
        num_layers_per_gpu[0] = math.ceil(num_layers_per_gpu[0] * 0.5)

        layer_cnt = 0
        for i, num_layer in enumerate(num_layers_per_gpu):
            for j in range(num_layer):
                device_map[f'language_model.model.layers.{layer_cnt}'] = i
                layer_cnt += 1

        # Vision and embedding components on GPU 0
        device_map['vision_model'] = 0
        device_map['mlp1'] = 0
        device_map['language_model.model.tok_embeddings'] = 0
        device_map['language_model.model.embed_tokens'] = 0
        device_map['language_model.output'] = 0
        device_map['language_model.model.norm'] = 0
        device_map['language_model.model.rotary_emb'] = 0
        device_map['language_model.lm_head'] = 0
        device_map[f'language_model.model.layers.{num_layers - 1}'] = 0

        return device_map
    except Exception:
        # Fallback to auto
        return "auto"

def create_quantization_config(precision="4bit"):
    """Create quantization configuration for memory efficiency with CUDA safety"""
    if precision == "4bit":
        return BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_compute_dtype=torch.bfloat16,  # Use bfloat16 for better stability
            bnb_4bit_quant_type="nf4",  # Normal float 4-bit
            bnb_4bit_use_double_quant=True,  # Double quantization for better compression
        )
    elif precision == "8bit":
        return BitsAndBytesConfig(
            load_in_8bit=True,
            llm_int8_threshold=6.0,
            llm_int8_has_fp16_weight=False,
        )
    else:
        return None

def initialize_internvl3_a40_optimized():
    """Initialize InternVL3-8B model optimized for A40 GPU with 8-bit precision"""

    model_id = "OpenGVLab/InternVL3-8B"

    print(f"🤖 Initializing InternVL3-8B for A40 GPU: {model_id}")
    print("🔢 Using 8-bit precision for A40 compatibility")
    print("📊 Setting up A40-optimized configuration...")

    try:
        # A40 has 48GB VRAM, so we can use single GPU
        device_map = "auto"  # Let transformers handle device placement

        print(f"💾 Target GPU: A40 (48GB VRAM)")
        print(f"📋 Using automatic device mapping for single GPU")

        # Load tokenizer
        print("🔄 Loading tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(
            model_id,
            trust_remote_code=True,
            use_fast=False
        )
        print("✅ Tokenizer loaded successfully")

        # Load model with A40-optimized 8-bit settings
        print("🔄 Loading model with A40-optimized 8-bit precision...")

        model = AutoModel.from_pretrained(
            model_id,
            torch_dtype=torch.bfloat16,  # Use bfloat16 as base dtype
            load_in_8bit=True,           # Enable 8-bit quantization
            low_cpu_mem_usage=True,      # Optimize CPU memory usage
            use_flash_attn=True,         # Enable flash attention for speed
            trust_remote_code=True,      # Required for InternVL3
            device_map=device_map        # Auto device placement
        ).eval()

        print("✅ Model loaded with 8-bit quantization for A40")

        # Verify model is properly quantized
        model_dtype = next(model.parameters()).dtype
        print(f"💾 Model device: {next(model.parameters()).device}")
        print(f"🔢 Model dtype: {model_dtype}")

        # Check memory usage
        if torch.cuda.is_available():
            memory_allocated = torch.cuda.memory_allocated() / 1024**3  # GB
            memory_reserved = torch.cuda.memory_reserved() / 1024**3   # GB
            print(f"📊 GPU Memory - Allocated: {memory_allocated:.1f}GB, Reserved: {memory_reserved:.1f}GB")
            print(f"📊 A40 Memory Usage: {memory_reserved/48*100:.1f}% of 48GB")

        return model, tokenizer, model_dtype

    except Exception as e:
        print(f"❌ Error loading InternVL3-8B model: {e}")

        # Provide A40-specific error messages
        if "CUDA" in str(e):
            print("\n🚨 A40 GPU Error:")
            print("1. Ensure A40 drivers are up to date")
            print("2. Check CUDA compatibility")
            print("3. Try reducing batch size")
        elif "flash_attn" in str(e):
            print("\n🚨 Flash Attention Error:")
            print("Try installing: pip install flash-attn --no-build-isolation")
        elif "bitsandbytes" in str(e):
            print("\n🚨 BitsAndBytes Error:")
            print("Try installing: pip install bitsandbytes")

        raise e

def initialize_internvl3(precision="8bit"):
    """Initialize InternVL3-8B model with configurable precision (legacy function)"""

    if precision == "8bit":
        # Use A40-optimized version for 8-bit
        return initialize_internvl3_a40_optimized()

    model_id = "OpenGVLab/InternVL3-8B"

    print(f"🤖 Initializing InternVL3-8B model: {model_id}")
    print(f"🔢 Precision mode: {precision}")
    print("📊 Setting up device mapping...")

    try:
        # Get device map
        device_map = split_model_device_map()
        gpu_count = torch.cuda.device_count()

        print(f"💾 Available GPUs: {gpu_count}")
        if isinstance(device_map, dict):
            print(f"📋 Using custom device map for {gpu_count} GPUs")
        else:
            print(f"📋 Using automatic device mapping")

        # Create quantization config
        quantization_config = create_quantization_config(precision)

        # Load tokenizer
        print("🔄 Loading tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(
            model_id,
            trust_remote_code=True,
            use_fast=False
        )
        print("✅ Tokenizer loaded successfully")

        # Load model with quantization
        print(f"🔄 Loading model with {precision} precision...")

        if precision == "4bit":
            # For 4-bit, use the original example settings
            model = AutoModel.from_pretrained(
                model_id,
                quantization_config=quantization_config,
                torch_dtype=torch.bfloat16,
                low_cpu_mem_usage=True,
                use_flash_attn=True,  # Keep flash attention as in original
                trust_remote_code=True,
                device_map=device_map
            ).eval()
            print("✅ Model loaded with 4-bit quantization")
            model_dtype = torch.bfloat16  # Approximate for 4-bit

        else:
            # Fallback to bfloat16 without quantization
            model = AutoModel.from_pretrained(
                model_id,
                torch_dtype=torch.bfloat16,
                low_cpu_mem_usage=True,
                use_flash_attn=True,
                trust_remote_code=True,
                device_map=device_map
            ).eval()
            print("✅ Model loaded with bfloat16 (no quantization)")
            model_dtype = torch.bfloat16

        print(f"💾 Model device: {next(model.parameters()).device}")
        print(f"🔢 Model dtype: {next(model.parameters()).dtype}")

        # Check actual memory usage
        if torch.cuda.is_available():
            memory_allocated = torch.cuda.memory_allocated() / 1024**3  # GB
            memory_reserved = torch.cuda.memory_reserved() / 1024**3   # GB
            print(f"📊 GPU Memory - Allocated: {memory_allocated:.1f}GB, Reserved: {memory_reserved:.1f}GB")

        return model, tokenizer, model_dtype

    except Exception as e:
        print(f"❌ Error loading InternVL3-8B model: {e}")

        # Provide helpful error messages
        if "CUDA" in str(e):
            print("\n🚨 CUDA/Memory Error:")
            print("1. Try 4-bit precision: precision='4bit'")
            print("2. Try 8-bit precision: precision='8bit'")
            print("3. Close other GPU processes")
        elif "flash_attn" in str(e):
            print("\n🚨 Flash Attention Error:")
            print("Try installing: pip install flash-attn")
        elif "bitsandbytes" in str(e):
            print("\n🚨 BitsAndBytes Error:")
            print("Try installing: pip install bitsandbytes")

        raise e

def create_generation_config(max_tokens=1024, temperature=0.3):
    """Create generation configuration"""
    return {
        'max_new_tokens': max_tokens,
        'temperature': temperature,
        'do_sample': True if temperature > 0 else False,
        'top_p': 0.9 if temperature > 0 else None,
        'repetition_penalty': 1.05
    }
