#!/usr/bin/env python3
"""
InternVL3-8B model initialization utilities
"""

import torch
import math
from transformers import AutoTokenizer, AutoModel, AutoConfig

def split_model_device_map(model_name="InternVL3-8B"):
    """Create device map for multi-GPU setup"""
    device_map = {}
    world_size = torch.cuda.device_count()
    
    if world_size == 1:
        # Single GPU setup
        return "auto"
    
    try:
        # Multi-GPU setup
        config = AutoConfig.from_pretrained("OpenGVLab/InternVL3-8B", trust_remote_code=True)
        num_layers = config.llm_config.num_hidden_layers
        
        # Since the first GPU will be used for ViT, treat it as half a GPU
        num_layers_per_gpu = math.ceil(num_layers / (world_size - 0.5))
        num_layers_per_gpu = [num_layers_per_gpu] * world_size
        num_layers_per_gpu[0] = math.ceil(num_layers_per_gpu[0] * 0.5)
        
        layer_cnt = 0
        for i, num_layer in enumerate(num_layers_per_gpu):
            for j in range(num_layer):
                device_map[f'language_model.model.layers.{layer_cnt}'] = i
                layer_cnt += 1
        
        # Vision and embedding components on GPU 0
        device_map['vision_model'] = 0
        device_map['mlp1'] = 0
        device_map['language_model.model.tok_embeddings'] = 0
        device_map['language_model.model.embed_tokens'] = 0
        device_map['language_model.output'] = 0
        device_map['language_model.model.norm'] = 0
        device_map['language_model.model.rotary_emb'] = 0
        device_map['language_model.lm_head'] = 0
        device_map[f'language_model.model.layers.{num_layers - 1}'] = 0
        
        return device_map
    except Exception:
        # Fallback to auto
        return "auto"

def initialize_internvl3():
    """Initialize InternVL3-8B model with optimal settings"""
    
    model_id = "OpenGVLab/InternVL3-8B"
    
    print(f"🤖 Initializing InternVL3-8B model: {model_id}")
    print("📊 Setting up device mapping...")
    
    try:
        # Get device map
        device_map = split_model_device_map()
        gpu_count = torch.cuda.device_count()
        
        print(f"💾 Available GPUs: {gpu_count}")
        if isinstance(device_map, dict):
            print(f"📋 Using custom device map for {gpu_count} GPUs")
        else:
            print(f"📋 Using automatic device mapping")
        
        # Load tokenizer
        print("🔄 Loading tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(
            model_id, 
            trust_remote_code=True, 
            use_fast=False
        )
        print("✅ Tokenizer loaded successfully")
        
        # Load model with optimal settings
        print("🔄 Loading model...")
        model = AutoModel.from_pretrained(
            model_id,
            torch_dtype=torch.bfloat16,
            load_in_8bit=True,  # Use 8-bit for memory efficiency
            low_cpu_mem_usage=True,
            use_flash_attn=True,
            trust_remote_code=True,
            device_map=device_map
        ).eval()
        
        print("✅ Model loaded successfully")
        print(f"💾 Model device: {next(model.parameters()).device}")
        print(f"🔢 Model dtype: {next(model.parameters()).dtype}")
        
        return model, tokenizer
        
    except Exception as e:
        print(f"❌ Error loading InternVL3-8B model: {e}")
        
        # Provide helpful error messages
        if "CUDA" in str(e):
            print("\n🚨 CUDA/Memory Error:")
            print("1. Try reducing load_in_8bit=True")
            print("2. Ensure sufficient GPU memory (recommended: 2x 80GB)")
            print("3. Close other GPU processes")
        elif "flash_attn" in str(e):
            print("\n🚨 Flash Attention Error:")
            print("Try installing: pip install flash-attn")
        
        raise e

def create_generation_config(max_tokens=1024, temperature=0.3):
    """Create generation configuration"""
    return {
        'max_new_tokens': max_tokens,
        'temperature': temperature,
        'do_sample': True if temperature > 0 else False,
        'top_p': 0.9 if temperature > 0 else None,
        'repetition_penalty': 1.05
    }
