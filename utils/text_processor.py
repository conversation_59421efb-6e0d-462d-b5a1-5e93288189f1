#!/usr/bin/env python3
"""
Text processing utilities for VLM inference
"""

import re

def remove_urls_from_text(text: str) -> tuple:
    """Remove all URLs from text to reduce token count significantly"""

    # Remove various URL patterns
    url_patterns = [
        r'https?://[^\s<>"]+',  # Standard HTTP/HTTPS URLs
        r'www\.[^\s<>"]+',      # www URLs without protocol
        r'ftp://[^\s<>"]+',     # FTP URLs
        r'[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(?:/[^\s<>"]*)?',  # Domain-like patterns
    ]

    cleaned_text = text
    urls_removed = 0

    for pattern in url_patterns:
        matches = re.findall(pattern, cleaned_text)
        urls_removed += len(matches)
        cleaned_text = re.sub(pattern, '', cleaned_text)

    # Clean up extra whitespace left by URL removal
    cleaned_text = re.sub(r'\s+', ' ', cleaned_text)  # Multiple spaces to single
    cleaned_text = re.sub(r'\n\s*\n\s*\n', '\n\n', cleaned_text)  # Multiple newlines
    cleaned_text = cleaned_text.strip()

    return cleaned_text, urls_removed

def estimate_tokens(text: str, num_images: int) -> dict:
    """Estimate token usage for text and images"""

    # Conservative token estimation for Phi-3-Vision
    chars_per_token = 2.5  # More conservative estimate
    tokens_per_image = 1200  # Conservative estimate for images

    text_tokens = len(text) / chars_per_token
    image_tokens = num_images * tokens_per_image
    total_tokens = text_tokens + image_tokens

    return {
        'text_tokens': int(text_tokens),
        'image_tokens': int(image_tokens),
        'total_tokens': int(total_tokens)
    }
