#!/usr/bin/env python3
"""
Image processing utilities for Gemma-3n inference
"""

import base64
import requests
from io import BytesIO
from PIL import Image

def prepare_image_for_gemma(base64_data: str) -> Image.Image:
    """Convert base64 to PIL Image for Gemma-3n"""
    try:
        if base64_data.startswith('data:'):
            header, base64_content = base64_data.split(',', 1)
        else:
            base64_content = base64_data.strip()

        image_bytes = base64.b64decode(base64_content)
        image = Image.open(BytesIO(image_bytes))

        if image.mode != 'RGB':
            image = image.convert('RGB')

        # Gemma-3n can handle larger images, but resize for efficiency
        max_size = 768
        if max(image.size) > max_size:
            original_size = image.size
            image.thumbnail((max_size, max_size), Image.Resampling.LANCZOS)
            print(f"    📏 Resized: {original_size} → {image.size}")

        return image
    except Exception as e:
        print(f"    ❌ Image error: {e}")
        return None

def download_image_from_url(url: str) -> Image.Image:
    """Download image from URL for testing"""
    try:
        response = requests.get(url)
        image = Image.open(BytesIO(response.content))
        if image.mode != 'RGB':
            image = image.convert('RGB')
        return image
    except Exception as e:
        print(f"❌ Error downloading image from {url}: {e}")
        return None
