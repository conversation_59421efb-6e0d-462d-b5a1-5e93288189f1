#!/usr/bin/env python3
"""
Phi-3-Vision Robust Inference
Enhanced with comprehensive logging, long response generation, and similarity analysis
Handles the "decoder prompt too long" issue with aggressive truncation and context management
"""

import json
import time
import base64
import logging
import difflib
from io import BytesIO
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple
from PIL import Image
from vllm import LLM, SamplingParams

def setup_logging() -> Tuple[logging.Logger, str]:
    """Setup comprehensive logging with file and console output"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = f"phi3_robust_inference_{timestamp}.log"

    # Create logs directory if it doesn't exist
    Path("logs").mkdir(exist_ok=True)
    log_path = Path("logs") / log_file

    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_path, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

    logger = logging.getLogger(__name__)
    logger.info(f"🗂️ Logging to: {log_path}")
    return logger, str(log_path)

def calculate_similarity(text1: str, text2: str) -> Dict[str, float]:
    """Calculate multiple similarity metrics between two texts"""

    # Basic similarity using difflib
    sequence_similarity = difflib.SequenceMatcher(None, text1, text2).ratio()

    # Word-level similarity
    words1 = set(text1.lower().split())
    words2 = set(text2.lower().split())

    if len(words1) == 0 and len(words2) == 0:
        word_similarity = 1.0
    elif len(words1) == 0 or len(words2) == 0:
        word_similarity = 0.0
    else:
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        word_similarity = intersection / union if union > 0 else 0.0

    # Length similarity
    len1, len2 = len(text1), len(text2)
    length_similarity = 1.0 - abs(len1 - len2) / max(len1, len2, 1)

    # Character-level similarity
    char_similarity = sum(1 for a, b in zip(text1, text2) if a == b) / max(len1, len2, 1)

    return {
        'sequence_similarity': sequence_similarity,
        'word_similarity': word_similarity,
        'length_similarity': length_similarity,
        'character_similarity': char_similarity,
        'average_similarity': (sequence_similarity + word_similarity + length_similarity + char_similarity) / 4
    }

def load_prompts_from_jsonl(file_path: str, logger: logging.Logger):
    """Load prompts from JSONL file with comprehensive logging"""
    logger.info(f"📂 Loading prompts from {file_path}...")

    try:
        prompts = []
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Handle formatted JSON records
        brace_count = 0
        current_record = ""

        for line in content.split('\n'):
            if line.strip() == '{' and brace_count == 0:
                if current_record.strip():
                    try:
                        record = json.loads(current_record.strip())
                        prompts.append(record)
                    except json.JSONDecodeError:
                        pass
                current_record = line + '\n'
                brace_count = 1
            elif brace_count > 0:
                current_record += line + '\n'
                brace_count += line.count('{') - line.count('}')

                if brace_count == 0:
                    try:
                        record = json.loads(current_record.strip())
                        prompts.append(record)
                    except json.JSONDecodeError:
                        pass
                    current_record = ""

        if current_record.strip():
            try:
                record = json.loads(current_record.strip())
                prompts.append(record)
            except json.JSONDecodeError:
                pass

        logger.info(f"✅ Loaded {len(prompts)} prompts successfully")
        return prompts

    except Exception as e:
        logger.error(f"❌ Error loading prompts: {str(e)}")
        return []

def prepare_image_conservative(base64_data: str) -> Image.Image:
    """Convert base64 to PIL Image with aggressive resizing to save tokens"""
    try:
        if base64_data.startswith('data:'):
            header, base64_content = base64_data.split(',', 1)
        else:
            base64_content = base64_data.strip()

        image_bytes = base64.b64decode(base64_content)
        image = Image.open(BytesIO(image_bytes))

        if image.mode != 'RGB':
            image = image.convert('RGB')

        # Aggressive resizing to minimize token usage
        max_size = 384  # Smaller than usual
        if max(image.size) > max_size:
            original_size = image.size
            image.thumbnail((max_size, max_size), Image.Resampling.LANCZOS)
            print(f"    📏 Aggressively resized: {original_size} → {image.size}")

        return image
    except Exception as e:
        print(f"    ❌ Image error: {e}")
        return None

def remove_urls_from_text(text: str) -> str:
    """Remove all URLs from text to reduce token count significantly"""
    import re

    # Remove various URL patterns
    url_patterns = [
        r'https?://[^\s<>"]+',  # Standard HTTP/HTTPS URLs
        r'www\.[^\s<>"]+',      # www URLs without protocol
        r'ftp://[^\s<>"]+',     # FTP URLs
        r'[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(?:/[^\s<>"]*)?',  # Domain-like patterns
    ]

    cleaned_text = text
    urls_removed = 0

    for pattern in url_patterns:
        matches = re.findall(pattern, cleaned_text)
        urls_removed += len(matches)
        cleaned_text = re.sub(pattern, '', cleaned_text)

    # Clean up extra whitespace left by URL removal
    cleaned_text = re.sub(r'\s+', ' ', cleaned_text)  # Multiple spaces to single
    cleaned_text = re.sub(r'\n\s*\n\s*\n', '\n\n', cleaned_text)  # Multiple newlines
    cleaned_text = cleaned_text.strip()

    return cleaned_text, urls_removed

def aggressive_truncate(system_prompt: str, user_prompt: str, target_chars: int) -> tuple:
    """Aggressively truncate prompts to fit in context"""

    print(f"  🔪 Aggressive truncation: {len(system_prompt) + len(user_prompt):,} → {target_chars:,} chars")

    # Very aggressive allocation
    system_ratio = 0.25  # Only 25% for system
    user_ratio = 0.75    # 75% for user (product data is more important)

    system_target = int(target_chars * system_ratio)
    user_target = int(target_chars * user_ratio)

    # System prompt truncation - keep only essential parts
    if len(system_prompt) > system_target:
        # Try to keep instructions only
        if "INSTRUCTIONS:" in system_prompt:
            instructions_start = system_prompt.find("INSTRUCTIONS:")
            instructions_part = system_prompt[instructions_start:]

            if len(instructions_part) <= system_target:
                truncated_system = instructions_part
            else:
                # Even instructions are too long, take first part
                truncated_system = instructions_part[:system_target]
        else:
            # No instructions section, take from end (usually instructions)
            truncated_system = system_prompt[-system_target:]
    else:
        truncated_system = system_prompt

    # User prompt truncation - preserve structure
    if len(user_prompt) > user_target:
        # Find key sections
        sections = user_prompt.split('\n\n')
        key_sections = []

        for section in sections:
            if any(keyword in section for keyword in ['Product details:', 'Enriched data:', 'Markdown content:']):
                key_sections.append(section)

        if key_sections:
            # Distribute space among key sections
            chars_per_section = user_target // len(key_sections)
            truncated_sections = []

            for section in key_sections:
                if len(section) <= chars_per_section:
                    truncated_sections.append(section)
                else:
                    # Take first part of section
                    truncated_sections.append(section[:chars_per_section])

            truncated_user = '\n\n'.join(truncated_sections)
        else:
            # No structure found, just truncate
            truncated_user = user_prompt[:user_target]
    else:
        truncated_user = user_prompt

    print(f"    System: {len(system_prompt):,} → {len(truncated_system):,} chars")
    print(f"    User: {len(user_prompt):,} → {len(truncated_user):,} chars")

    return truncated_system, truncated_user

def initialize_phi3_with_fallback():
    """Initialize Phi-3-Vision with progressive fallback - memory-aware context sizing"""

    # Memory-aware context attempts based on GPU memory limitations
    # vLLM error shows max possible is ~49,584 tokens with 18.16 GiB available
    context_attempts = [
        {"context": 120000, "gpu_util": 0.92, "description": "49k context (near GPU memory limit)"},
        {"context": 100000, "gpu_util": 0.9, "description": "49k context (near GPU memory limit)"},
        {"context": 90000, "gpu_util": 0.9, "description": "49k context (near GPU memory limit)"},
        {"context": 70000, "gpu_util": 0.85, "description": "49k context (near GPU memory limit)"},
        {"context": 60000, "gpu_util": 0.85, "description": "49k context (near GPU memory limit)"},
        {"context": 49000, "gpu_util": 0.85, "description": "49k context (near GPU memory limit)"},
        {"context": 45000, "gpu_util": 0.8, "description": "45k context (safe)"},
        {"context": 40000, "gpu_util": 0.75, "description": "40k context"},
        {"context": 35000, "gpu_util": 0.7, "description": "35k context"},
        {"context": 30000, "gpu_util": 0.65, "description": "30k context"},
        {"context": 25000, "gpu_util": 0.6, "description": "25k context"},
        {"context": 20000, "gpu_util": 0.55, "description": "20k context"},
        {"context": 16000, "gpu_util": 0.5, "description": "16k context (minimal)"}
    ]

    for attempt in context_attempts:
        try:
            print(f"  🔄 Trying {attempt['description']}...")

            # Calculate estimated KV cache memory needed
            estimated_kv_memory = (attempt["context"] / 1000) * 0.9  # Rough estimate: ~0.9 GB per 1k tokens
            print(f"    📊 Estimated KV cache needed: ~{estimated_kv_memory:.1f} GiB")

            llm = LLM(
                model="microsoft/Phi-3-vision-128k-instruct",
                trust_remote_code=True,
                max_model_len=attempt["context"],
                gpu_memory_utilization=attempt["gpu_util"],
                swap_space=4,  # Reduced swap to save memory
                max_num_seqs=1,
                limit_mm_per_prompt={"image": 2},  # Limit to 2 images
                enforce_eager=True,  # Use eager mode to save memory
                # Note: Paged attention is enabled by default in modern vLLM
            )

            print(f"  ✅ Success with {attempt['description']}")
            print(f"    💾 GPU utilization: {attempt['gpu_util']*100:.0f}%")
            return llm, attempt["context"]

        except Exception as e:
            error_msg = str(e)
            print(f"  ❌ Failed: {error_msg[:150]}...")

            # Check if it's a memory error and provide specific guidance
            if "KV cache" in error_msg and "memory" in error_msg:
                print(f"    💡 Memory issue detected - trying smaller context...")
            elif "CUDA out of memory" in error_msg:
                print(f"    💡 GPU memory exhausted - reducing utilization...")

            continue

    raise Exception("Could not initialize Phi-3-Vision with any context size")

def main():
    """Enhanced Robust Phi-3-Vision inference with logging and similarity analysis"""

    # Setup comprehensive logging
    logger, log_file = setup_logging()

    logger.info("🛡️ Phi-3-Vision Robust Inference - Enhanced Edition")
    logger.info("=" * 60)
    logger.info("🎯 Features: Logging, Long responses, Similarity analysis")

    # Load prompts
    input_files = ['final_image_prompts_cleaned.jsonl', 'scrape_content_prompts.jsonl']
    prompts = None

    for input_file in input_files:
        prompts = load_prompts_from_jsonl(input_file, logger)
        if prompts:
            logger.info(f"✅ Using prompts from: {input_file}")
            break

    if not prompts:
        logger.error("❌ No prompts loaded")
        return

    # Initialize model with fallback
    logger.info("\n🤖 INITIALIZING PHI-3-VISION WITH FALLBACK")
    logger.info("-" * 50)

    try:
        llm, max_context = initialize_phi3_with_fallback()
        logger.info(f"✅ Model initialized with {max_context:,} token context")
    except Exception as e:
        logger.error(f"❌ All initialization attempts failed: {e}")
        return

    # Process prompts with enhanced logging and analysis
    results = []
    max_prompts = min(len(prompts), 5)  # Process up to 5 prompts

    logger.info(f"\n🚀 PROCESSING {max_prompts} PROMPTS")
    logger.info("=" * 60)

    for i, prompt_data in enumerate(prompts[:max_prompts], 1):
        logger.info(f"\n{'='*20} PROMPT {i}/{max_prompts} {'='*20}")

        try:
            # Extract data
            system_prompt = prompt_data.get('SystemPrompt', '')
            user_prompt = prompt_data.get('UserPrompt', '')
            image_data_urls = prompt_data.get('Images', [])
            actual_response = prompt_data.get('Response', '')

            logger.info(f"📋 Original prompt lengths:")
            logger.info(f"    System: {len(system_prompt):,} chars")
            logger.info(f"    User: {len(user_prompt):,} chars")
            logger.info(f"    Images: {len(image_data_urls)}")
            logger.info(f"    Actual response: {len(actual_response):,} chars")

            # Remove URLs first to maximize available context
            logger.info("🔗 Removing URLs to maximize context...")
            cleaned_system, system_urls = remove_urls_from_text(system_prompt)
            cleaned_user, user_urls = remove_urls_from_text(user_prompt)

            total_urls_removed = system_urls + user_urls
            total_chars_saved = (len(system_prompt) - len(cleaned_system)) + (len(user_prompt) - len(cleaned_user))

            logger.info("📝 URL removal results:")
            logger.info(f"    URLs removed: {total_urls_removed}")
            logger.info(f"    Characters saved: {total_chars_saved:,}")
            logger.info(f"    System: {len(system_prompt):,} → {len(cleaned_system):,} chars")
            logger.info(f"    User: {len(user_prompt):,} → {len(cleaned_user):,} chars")

            # Use cleaned prompts
            system_prompt = cleaned_system
            user_prompt = cleaned_user

            # Process only 2 images to save tokens
            processed_images = []
            for j, img_data in enumerate(image_data_urls[:2]):
                image = prepare_image_conservative(img_data)
                if image:
                    processed_images.append(image)
                    print(f"    ✓ Image {j+1}: {image.size}")

            if not processed_images:
                print("  ❌ No valid images")
                continue

            # Calculate safe token budget based on actual available context
            # After URL removal, we can be more generous with text allocation
            reserved_for_images = len(processed_images) * 1200  # Reduced estimate after URL removal
            reserved_for_response = 2000  # Space for good response
            available_for_text = max_context - reserved_for_images - reserved_for_response

            # Convert to characters (more generous after URL removal)
            target_chars = int(available_for_text * 2.5)  # 2.5 chars per token (better after URL removal)

            print(f"  📊 Token budget (after URL removal):")
            print(f"    Max context: {max_context:,}")
            print(f"    Reserved for images: {reserved_for_images:,}")
            print(f"    Reserved for response: {reserved_for_response:,}")
            print(f"    Available for text: {available_for_text:,} tokens")
            print(f"    Target text chars: {target_chars:,}")

            # Check if we even need truncation after URL removal
            current_chars = len(system_prompt) + len(user_prompt)
            if current_chars <= target_chars:
                print(f"  🎉 No truncation needed! Current: {current_chars:,}, Target: {target_chars:,}")
                final_system = system_prompt
                final_user = user_prompt
            else:
                print(f"  ⚠️ Truncation needed: {current_chars:,} → {target_chars:,} chars")

                # Apply aggressive truncation only if needed
                final_system, final_user = aggressive_truncate(system_prompt, user_prompt, target_chars)

            # Create Phi-3-Vision prompt
            image_tokens = "".join([f"<|image_{i+1}|>" for i in range(len(processed_images))])
            full_prompt = f"<|user|>\n{image_tokens}\n{final_system}\n\n{final_user}<|end|>\n<|assistant|>\n"

            print(f"  📏 Final prompt: {len(full_prompt):,} chars")

            # Enhanced sampling parameters for longer responses
            target_response_length = len(actual_response) if actual_response else 2000
            max_output_tokens = max(2000, min(4000, target_response_length // 3))  # Aim for at least as long as actual

            sampling_params = SamplingParams(
                temperature=0.1,
                top_p=0.9,
                max_tokens=max_output_tokens,
                repetition_penalty=1.05,
                stop=["<|end|>", "<|user|>", "<|assistant|>"]
            )

            logger.info(f"📝 Target response length: {target_response_length:,} chars")
            logger.info(f"📝 Max output tokens: {max_output_tokens:,}")

            # Run inference
            logger.info("🚀 Running enhanced robust inference...")
            start_time = time.time()

            outputs = llm.generate(
                [{
                    "prompt": full_prompt,
                    "multi_modal_data": {"image": processed_images}
                }],
                sampling_params=sampling_params
            )

            inference_time = time.time() - start_time

            if outputs and outputs[0].outputs:
                generated_text = outputs[0].outputs[0].text.strip()

                logger.info(f"✅ Inference completed in {inference_time:.2f}s")
                logger.info(f"📝 Generated response: {len(generated_text):,} chars")

                # Calculate similarity with actual response
                if actual_response:
                    similarity_metrics = calculate_similarity(generated_text, actual_response)

                    logger.info("🔍 SIMILARITY ANALYSIS:")
                    logger.info(f"    Sequence similarity: {similarity_metrics['sequence_similarity']:.3f}")
                    logger.info(f"    Word similarity: {similarity_metrics['word_similarity']:.3f}")
                    logger.info(f"    Length similarity: {similarity_metrics['length_similarity']:.3f}")
                    logger.info(f"    Character similarity: {similarity_metrics['character_similarity']:.3f}")
                    logger.info(f"    📊 AVERAGE SIMILARITY: {similarity_metrics['average_similarity']:.3f}")

                    # Length comparison
                    length_ratio = len(generated_text) / len(actual_response) if len(actual_response) > 0 else 0
                    logger.info(f"📏 Length comparison:")
                    logger.info(f"    Generated: {len(generated_text):,} chars")
                    logger.info(f"    Actual: {len(actual_response):,} chars")
                    logger.info(f"    Ratio: {length_ratio:.2f}x")

                    if length_ratio >= 0.8:
                        logger.info("    ✅ Generated response is adequately long")
                    else:
                        logger.warning("    ⚠️ Generated response is shorter than expected")
                else:
                    similarity_metrics = None
                    logger.warning("⚠️ No actual response available for comparison")

                logger.info(f"📄 Response preview: {generated_text[:300]}...")

                results.append({
                    'prompt_id': f"prompt_{i}",
                    'max_context_used': max_context,
                    'original_system_length': len(system_prompt),
                    'original_user_length': len(user_prompt),
                    'final_system_length': len(final_system),
                    'final_user_length': len(final_user),
                    'urls_removed': total_urls_removed,
                    'chars_saved': total_chars_saved,
                    'compression_ratio': (len(final_system) + len(final_user)) / (len(system_prompt) + len(user_prompt)),
                    'actual_response': actual_response,
                    'generated_response': generated_text,
                    'actual_length': len(actual_response),
                    'generated_length': len(generated_text),
                    'length_ratio': len(generated_text) / len(actual_response) if len(actual_response) > 0 else 0,
                    'similarity_metrics': similarity_metrics,
                    'inference_time': inference_time,
                    'target_tokens': max_output_tokens,
                    'success': True
                })

            else:
                logger.error("❌ No output generated")

        except Exception as e:
            logger.error(f"❌ Error processing prompt {i}: {e}")
            results.append({
                'prompt_id': f"prompt_{i}",
                'success': False,
                'error': str(e)
            })

    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f'phi3_robust_results_{timestamp}.json'

    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)

    # Enhanced summary with similarity analysis
    successful = [r for r in results if r.get('success', False)]

    logger.info(f"\n📊 ENHANCED SUMMARY:")
    logger.info("=" * 60)
    logger.info(f"🖥️ Context used: {max_context:,} tokens")
    logger.info(f"📊 Processed: {len(results)} prompts")
    logger.info(f"✅ Successful: {len(successful)}")

    if successful:
        # Calculate averages
        avg_compression = sum(r['compression_ratio'] for r in successful) / len(successful)
        avg_time = sum(r['inference_time'] for r in successful) / len(successful)
        avg_length_ratio = sum(r.get('length_ratio', 0) for r in successful) / len(successful)

        # Similarity metrics
        similarity_results = [r for r in successful if r.get('similarity_metrics')]
        if similarity_results:
            avg_similarity = sum(r['similarity_metrics']['average_similarity'] for r in similarity_results) / len(similarity_results)
            logger.info(f"🔍 Average similarity: {avg_similarity:.3f}")

        logger.info(f"📏 Average length ratio: {avg_length_ratio:.2f}x")
        logger.info(f"🗜️ Average compression: {avg_compression:.1%}")
        logger.info(f"⏱️ Average inference time: {avg_time:.2f}s")

        # URL removal statistics
        total_urls = sum(r.get('urls_removed', 0) for r in successful)
        total_saved = sum(r.get('chars_saved', 0) for r in successful)
        logger.info(f"🔗 Total URLs removed: {total_urls:,}")
        logger.info(f"💾 Total characters saved: {total_saved:,}")

        logger.info(f"\n📋 DETAILED RESULTS:")
        for result in successful:
            logger.info(f"    {result['prompt_id']}:")
            logger.info(f"      Generated: {result['generated_length']:,} chars")
            logger.info(f"      Actual: {result['actual_length']:,} chars")
            logger.info(f"      Length ratio: {result.get('length_ratio', 0):.2f}x")
            if result.get('similarity_metrics'):
                logger.info(f"      Similarity: {result['similarity_metrics']['average_similarity']:.3f}")

    logger.info(f"\n💾 Results saved to: {output_file}")
    logger.info(f"📋 Logs saved to: {log_file}")
    logger.info("🎉 Enhanced robust inference completed!")

if __name__ == "__main__":
    main()
