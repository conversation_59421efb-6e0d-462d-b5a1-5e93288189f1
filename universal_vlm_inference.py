#!/usr/bin/env python3
"""
Universal VLM Inference
Supports all vLLM-compatible VLMs with interactive model selection and automatic truncation
"""

import json
import time
import base64
import re
from io import BytesIO
from datetime import datetime
from PIL import Image
from vllm import LLM, Sam<PERSON><PERSON><PERSON><PERSON>
from typing import Dict, List, Any, Tuple

# Comprehensive VLM model database
VLM_MODELS = {
    "1": {
        "name": "Phi-3-Vision 128k (Full Context)",
        "model_id": "microsoft/Phi-3-vision-128k-instruct",
        "max_context": 131072,
        "recommended_context": 120000,  # Use nearly full 128k context after URL removal
        "image_token_format": "<|image_{i}|>",
        "chat_template": "<|user|>\n{images}\n{prompt}<|end|>\n<|assistant|>\n",
        "stop_tokens": ["<|end|>", "<|user|>", "<|assistant|>"],
        "strengths": ["Ultra-long context", "URL removal", "Full 128k usage"],
        "memory_gb": 12,
        "token_multiplier": 1.5  # Reduced after URL removal
    },
    "2": {
        "name": "LLaVA-1.5-7B",
        "model_id": "llava-hf/llava-1.5-7b-hf",
        "max_context": 4096,
        "recommended_context": 3500,
        "image_token_format": "<image>",
        "chat_template": "USER: {images}\n{prompt}\nASSISTANT:",
        "stop_tokens": ["USER:", "ASSISTANT:", "</s>"],
        "strengths": ["Well-tested", "Stable", "Good baseline"],
        "memory_gb": 14
    },
    "3": {
        "name": "LLaVA-1.5-13B",
        "model_id": "llava-hf/llava-1.5-13b-hf",
        "max_context": 4096,
        "recommended_context": 3500,
        "image_token_format": "<image>",
        "chat_template": "USER: {images}\n{prompt}\nASSISTANT:",
        "stop_tokens": ["USER:", "ASSISTANT:", "</s>"],
        "strengths": ["Higher quality", "Better reasoning"],
        "memory_gb": 26
    },
    "4": {
        "name": "LLaVA-v1.6-Mistral-7B",
        "model_id": "llava-hf/llava-v1.6-mistral-7b-hf",
        "max_context": 32768,
        "recommended_context": 28000,
        "image_token_format": "<image>",
        "chat_template": "USER: {images}\n{prompt}\nASSISTANT:",
        "stop_tokens": ["USER:", "ASSISTANT:", "</s>"],
        "strengths": ["Long context", "Mistral base", "Good quality"],
        "memory_gb": 14
    },
    "5": {
        "name": "LLaVA-v1.6-Vicuna-7B",
        "model_id": "llava-hf/llava-v1.6-vicuna-7b-hf",
        "max_context": 4096,
        "recommended_context": 3500,
        "image_token_format": "<image>",
        "chat_template": "USER: {images}\n{prompt}\nASSISTANT:",
        "stop_tokens": ["USER:", "ASSISTANT:", "</s>"],
        "strengths": ["Vicuna base", "Good instruction following"],
        "memory_gb": 14
    },
    "6": {
        "name": "LLaVA-v1.6-Vicuna-13B",
        "model_id": "llava-hf/llava-v1.6-vicuna-13b-hf",
        "max_context": 4096,
        "recommended_context": 3500,
        "image_token_format": "<image>",
        "chat_template": "USER: {images}\n{prompt}\nASSISTANT:",
        "stop_tokens": ["USER:", "ASSISTANT:", "</s>"],
        "strengths": ["Large model", "High quality"],
        "memory_gb": 26
    },
    "7": {
        "name": "Qwen2-VL-2B",
        "model_id": "Qwen/Qwen2-VL-2B-Instruct",
        "max_context": 32768,
        "recommended_context": 28000,
        "image_token_format": "<img>",
        "chat_template": "<|im_start|>user\n{images}{prompt}<|im_end|>\n<|im_start|>assistant\n",
        "stop_tokens": ["<|im_end|>", "<|im_start|>"],
        "strengths": ["Small size", "Fast", "Long context"],
        "memory_gb": 6
    },
    "8": {
        "name": "Qwen2-VL-7B",
        "model_id": "Qwen/Qwen2-VL-7B-Instruct",
        "max_context": 32768,
        "recommended_context": 28000,
        "image_token_format": "<img>",
        "chat_template": "<|im_start|>user\n{images}{prompt}<|im_end|>\n<|im_start|>assistant\n",
        "stop_tokens": ["<|im_end|>", "<|im_start|>"],
        "strengths": ["Good balance", "Long context", "High quality"],
        "memory_gb": 14
    },
    "9": {
        "name": "InternVL2-8B",
        "model_id": "OpenGVLab/InternVL2-8B",
        "max_context": 8192,
        "recommended_context": 7000,
        "image_token_format": "<image>",
        "chat_template": "User: {images}\n{prompt}\nAssistant:",
        "stop_tokens": ["User:", "Assistant:", "</s>"],
        "strengths": ["Strong vision", "Good reasoning"],
        "memory_gb": 16
    }
}

def display_model_menu():
    """Display interactive model selection menu"""
    print("\n🤖 AVAILABLE VLM MODELS")
    print("=" * 80)

    for key, model in VLM_MODELS.items():
        print(f"{key}. {model['name']}")
        print(f"   Model: {model['model_id']}")
        print(f"   Context: {model['max_context']:,} tokens (recommended: {model['recommended_context']:,})")
        print(f"   Memory: ~{model['memory_gb']}GB")
        print(f"   Strengths: {', '.join(model['strengths'])}")
        print()

    while True:
        choice = input("🎯 Select model (1-9) or 'q' to quit: ").strip()

        if choice.lower() == 'q':
            return None

        if choice in VLM_MODELS:
            selected = VLM_MODELS[choice]
            print(f"\n✅ Selected: {selected['name']}")
            return selected

        print("❌ Invalid choice. Please select 1-9 or 'q'.")

def load_prompts_from_jsonl(file_path: str) -> List[Dict]:
    """Load prompts from JSONL file with error handling"""
    print(f"📂 Loading prompts from {file_path}...")

    try:
        prompts = []
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Handle both single-line and multi-line JSON
        if content.strip().startswith('['):
            # Regular JSON array
            prompts = json.loads(content)
        else:
            # JSONL or formatted JSON
            brace_count = 0
            current_record = ""

            for line in content.split('\n'):
                if line.strip() == '{' and brace_count == 0:
                    if current_record.strip():
                        try:
                            record = json.loads(current_record.strip())
                            prompts.append(record)
                        except json.JSONDecodeError:
                            pass
                    current_record = line + '\n'
                    brace_count = 1
                elif brace_count > 0:
                    current_record += line + '\n'
                    brace_count += line.count('{') - line.count('}')

                    if brace_count == 0:
                        try:
                            record = json.loads(current_record.strip())
                            prompts.append(record)
                        except json.JSONDecodeError:
                            pass
                        current_record = ""

            # Handle last record
            if current_record.strip():
                try:
                    record = json.loads(current_record.strip())
                    prompts.append(record)
                except json.JSONDecodeError:
                    pass

        print(f"✅ Loaded {len(prompts)} prompts")
        return prompts

    except Exception as e:
        print(f"❌ Error loading prompts: {e}")
        return []

def prepare_image(base64_data: str) -> Image.Image:
    """Convert base64 to PIL Image with error handling"""
    try:
        if base64_data.startswith('data:'):
            header, base64_content = base64_data.split(',', 1)
        else:
            base64_content = base64_data.strip()

        image_bytes = base64.b64decode(base64_content)
        image = Image.open(BytesIO(image_bytes))

        if image.mode != 'RGB':
            image = image.convert('RGB')

        # Moderate resizing for efficiency
        max_size = 768
        if max(image.size) > max_size:
            original_size = image.size
            image.thumbnail((max_size, max_size), Image.Resampling.LANCZOS)
            print(f"    📏 Resized: {original_size} → {image.size}")

        return image
    except Exception as e:
        print(f"    ❌ Image error: {e}")
        return None

def estimate_tokens(text: str, num_images: int, model_config: Dict) -> Dict:
    """Estimate token usage for a given model with conservative estimates"""
    # Different models have different tokenization efficiency
    # Use more conservative estimates based on real-world experience
    if "phi3" in model_config["model_id"].lower():
        chars_per_token = 2.5  # More conservative for Phi-3-Vision
        tokens_per_image = 1200  # Much higher - images take more tokens than expected
    elif "qwen" in model_config["model_id"].lower():
        chars_per_token = 3.0
        tokens_per_image = 800
    elif "llava" in model_config["model_id"].lower():
        chars_per_token = 3.5
        tokens_per_image = 1000
    else:
        chars_per_token = 3.0
        tokens_per_image = 800

    # Apply token multiplier if specified
    if "token_multiplier" in model_config:
        tokens_per_image *= model_config["token_multiplier"]

    text_tokens = len(text) / chars_per_token
    image_tokens = num_images * tokens_per_image
    total_tokens = text_tokens + image_tokens

    # Use 80% of recommended context as safe limit
    safe_limit = model_config['recommended_context'] * 0.8

    return {
        'text_tokens': int(text_tokens),
        'image_tokens': int(image_tokens),
        'total_tokens': int(total_tokens),
        'safe_limit': int(safe_limit),
        'fits_in_context': total_tokens < safe_limit
    }

def remove_urls_from_text(text: str) -> str:
    """Remove all URLs from text to reduce token count"""
    import re

    # Remove various URL patterns
    url_patterns = [
        r'https?://[^\s<>"]+',  # Standard HTTP/HTTPS URLs
        r'www\.[^\s<>"]+',      # www URLs without protocol
        r'ftp://[^\s<>"]+',     # FTP URLs
        r'[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(?:/[^\s<>"]*)?',  # Domain-like patterns
    ]

    cleaned_text = text
    urls_removed = 0

    for pattern in url_patterns:
        matches = re.findall(pattern, cleaned_text)
        urls_removed += len(matches)
        cleaned_text = re.sub(pattern, '', cleaned_text)

    # Clean up extra whitespace left by URL removal
    cleaned_text = re.sub(r'\s+', ' ', cleaned_text)  # Multiple spaces to single
    cleaned_text = re.sub(r'\n\s*\n\s*\n', '\n\n', cleaned_text)  # Multiple newlines
    cleaned_text = cleaned_text.strip()

    if urls_removed > 0:
        print(f"    🔗 Removed {urls_removed} URLs from text")

    return cleaned_text

def smart_truncate_prompt(system_prompt: str, user_prompt: str, target_chars: int) -> Tuple[str, str]:
    """Intelligently truncate prompts while preserving important content"""

    total_chars = len(system_prompt) + len(user_prompt)

    if total_chars <= target_chars:
        return system_prompt, user_prompt

    print(f"  📝 Truncating prompts: {total_chars:,} → {target_chars:,} chars")

    # Strategy: Preserve system prompt as much as possible, truncate user prompt intelligently
    system_ratio = 0.4  # Give 40% to system prompt
    user_ratio = 0.6    # Give 60% to user prompt

    system_target = int(target_chars * system_ratio)
    user_target = int(target_chars * user_ratio)

    # Truncate system prompt if needed
    if len(system_prompt) > system_target:
        # Try to keep the schema and instructions
        if "RESPONSE SCHEMA:" in system_prompt and "INSTRUCTIONS:" in system_prompt:
            schema_start = system_prompt.find("RESPONSE SCHEMA:")
            instructions_start = system_prompt.find("INSTRUCTIONS:")

            if schema_start < instructions_start:
                # Keep instructions and truncate schema
                instructions_part = system_prompt[instructions_start:]
                schema_part = system_prompt[schema_start:instructions_start]

                available_for_schema = system_target - len(instructions_part)
                if available_for_schema > 0:
                    truncated_schema = schema_part[:available_for_schema]
                    system_prompt = truncated_schema + instructions_part
                else:
                    system_prompt = instructions_part[:system_target]
            else:
                system_prompt = system_prompt[:system_target]
        else:
            system_prompt = system_prompt[:system_target]

    # Truncate user prompt intelligently
    if len(user_prompt) > user_target:
        # Try to preserve structure
        sections = user_prompt.split('\n\n')

        # Prioritize sections
        important_sections = []
        for section in sections:
            if any(keyword in section for keyword in ['Product details:', 'Enriched data:', 'Markdown content:']):
                important_sections.append(section)

        if important_sections:
            # Distribute space among important sections
            chars_per_section = user_target // len(important_sections)
            truncated_sections = []

            for section in important_sections:
                if len(section) <= chars_per_section:
                    truncated_sections.append(section)
                else:
                    # Truncate at sentence boundary if possible
                    truncated = section[:chars_per_section]
                    last_sentence = truncated.rfind('. ')
                    if last_sentence > chars_per_section * 0.8:
                        truncated = section[:last_sentence + 2]
                    truncated_sections.append(truncated)

            user_prompt = '\n\n'.join(truncated_sections)
        else:
            user_prompt = user_prompt[:user_target]

    return system_prompt, user_prompt

def create_model_prompt(system_prompt: str, user_prompt: str, images: List[Image.Image], model_config: Dict) -> str:
    """Create model-specific prompt format"""

    # Create image tokens
    if "phi3" in model_config["model_id"].lower():
        image_tokens = "".join([f"<|image_{i+1}|>" for i in range(len(images))])
    elif "qwen" in model_config["model_id"].lower():
        image_tokens = "<img>" * len(images)
    else:  # LLaVA and others
        image_tokens = "<image>\n" * len(images)

    # Combine prompts
    full_prompt = f"{system_prompt}\n\n{user_prompt}"

    # Apply chat template
    formatted_prompt = model_config["chat_template"].format(
        images=image_tokens,
        prompt=full_prompt
    )

    return formatted_prompt

def main():
    """Main universal VLM inference"""
    print("🌟 Universal VLM Inference with Auto-Truncation")
    print("=" * 60)

    # Model selection
    model_config = display_model_menu()
    if not model_config:
        print("👋 Goodbye!")
        return

    # Load prompts
    input_files = [
        'final_image_prompts_cleaned.jsonl',
        'scrape_content_prompts.jsonl',
        'final_image_prompts.jsonl'
    ]

    prompts = None
    for input_file in input_files:
        prompts = load_prompts_from_jsonl(input_file)
        if prompts:
            print(f"✅ Loaded prompts from: {input_file}")
            break

    if not prompts:
        print("❌ No prompts found")
        return

    # Initialize model
    print(f"\n🤖 INITIALIZING {model_config['name']}")
    print("-" * 50)

    try:
        # For Phi-3-Vision, try memory-aware context sizing
        if "phi3" in model_config["model_id"].lower():
            # Memory-aware context attempts (based on typical GPU memory limits)
            context_attempts = [49000, 45000, 40000, 35000, 30000, 25000, 20000]
            successful_context = None

            print(f"  💡 Using memory-aware context sizing for Phi-3-Vision")

            for context_size in context_attempts:
                try:
                    print(f"  🔄 Trying {context_size:,} token context...")

                    # Calculate estimated memory usage
                    estimated_memory = (context_size / 1000) * 0.9
                    print(f"    📊 Estimated KV cache: ~{estimated_memory:.1f} GiB")

                    llm = LLM(
                        model=model_config["model_id"],
                        trust_remote_code=True,
                        max_model_len=context_size,
                        gpu_memory_utilization=0.8,  # Reasonable utilization
                        swap_space=4,
                        max_num_seqs=1,
                        limit_mm_per_prompt={"image": 3},
                        enforce_eager=True,  # Save memory
                        # Note: Paged attention is enabled by default in modern vLLM
                    )
                    successful_context = context_size
                    print(f"  ✅ Successfully loaded with {context_size:,} tokens")
                    break
                except Exception as e:
                    error_msg = str(e)
                    print(f"  ❌ {context_size:,} tokens failed: {error_msg[:100]}...")

                    if "KV cache" in error_msg and "memory" in error_msg:
                        print(f"    💡 KV cache memory limit hit - trying smaller context")

                    continue

            if successful_context is None:
                print("❌ All context sizes failed")
                return

            # Update model config with successful context
            model_config["recommended_context"] = successful_context

        else:
            # For other models, use standard initialization
            llm = LLM(
                model=model_config["model_id"],
                trust_remote_code=True,
                max_model_len=model_config["recommended_context"],
                gpu_memory_utilization=0.8,
                max_num_seqs=1,
                limit_mm_per_prompt={"image": 3},
                # Note: Paged attention is enabled by default in modern vLLM
            )
            print(f"✅ Model loaded with {model_config['recommended_context']:,} token context")

    except Exception as e:
        print(f"❌ Model loading failed: {e}")
        return

    # Process prompts
    results = []
    max_prompts = min(len(prompts), 5)  # Process first 5

    for i, prompt_data in enumerate(prompts[:max_prompts], 1):
        print(f"\n{'='*20} PROMPT {i}/{max_prompts} {'='*20}")

        try:
            # Extract data
            system_prompt = prompt_data.get('SystemPrompt', '')
            user_prompt = prompt_data.get('UserPrompt', '')
            image_data_urls = prompt_data.get('Images', [])
            actual_response = prompt_data.get('Response', '')

            print(f"📋 Original lengths:")
            print(f"  System: {len(system_prompt):,} chars")
            print(f"  User: {len(user_prompt):,} chars")
            print(f"  Images: {len(image_data_urls)}")

            # Remove URLs from prompts to reduce token count
            print(f"  🔗 Removing URLs to maximize context usage...")
            cleaned_system = remove_urls_from_text(system_prompt)
            cleaned_user = remove_urls_from_text(user_prompt)

            print(f"  📝 After URL removal:")
            print(f"    System: {len(system_prompt):,} → {len(cleaned_system):,} chars ({len(system_prompt)-len(cleaned_system):,} saved)")
            print(f"    User: {len(user_prompt):,} → {len(cleaned_user):,} chars ({len(user_prompt)-len(cleaned_user):,} saved)")

            # Use cleaned prompts
            system_prompt = cleaned_system
            user_prompt = cleaned_user

            # Process images (limit to 3 for efficiency)
            processed_images = []
            for j, img_data in enumerate(image_data_urls[:3]):
                image = prepare_image(img_data)
                if image:
                    processed_images.append(image)
                    print(f"    ✓ Image {j+1}: {image.size}")

            if not processed_images:
                print("  ❌ No valid images")
                continue

            # Estimate initial token usage
            initial_prompt = f"{system_prompt}\n\n{user_prompt}"
            token_estimate = estimate_tokens(initial_prompt, len(processed_images), model_config)

            print(f"  🔢 Initial estimate: {token_estimate['total_tokens']:,} tokens")
            print(f"  📊 Context limit: {model_config['recommended_context']:,} tokens")

            # Auto-truncate if needed
            if not token_estimate['fits_in_context']:
                print(f"  ⚠️ Prompt too long, applying auto-truncation...")

                # Reserve tokens for images and response
                reserved_tokens = len(processed_images) * 500 + 1000  # Images + response buffer
                available_for_text = model_config['recommended_context'] - reserved_tokens
                target_chars = int(available_for_text * 3.5)  # Conservative conversion

                system_prompt, user_prompt = smart_truncate_prompt(
                    system_prompt, user_prompt, target_chars
                )

                print(f"  📝 Truncated lengths:")
                print(f"    System: {len(system_prompt):,} chars")
                print(f"    User: {len(user_prompt):,} chars")

            # Create final prompt
            final_prompt = create_model_prompt(system_prompt, user_prompt, processed_images, model_config)

            # Final token check
            final_estimate = estimate_tokens(final_prompt, len(processed_images), model_config)
            print(f"  ✅ Final estimate: {final_estimate['total_tokens']:,} tokens")

            # Sampling parameters
            max_output_tokens = min(2048, model_config['recommended_context'] - final_estimate['total_tokens'] - 500)

            sampling_params = SamplingParams(
                temperature=0.1,
                top_p=0.9,
                max_tokens=max_output_tokens,
                repetition_penalty=1.05,
                stop=model_config["stop_tokens"]
            )

            print(f"  📝 Max output tokens: {max_output_tokens}")

            # Run inference
            print("  🚀 Running inference...")
            start_time = time.time()

            outputs = llm.generate(
                [{
                    "prompt": final_prompt,
                    "multi_modal_data": {"image": processed_images}
                }],
                sampling_params=sampling_params
            )

            inference_time = time.time() - start_time

            if outputs and outputs[0].outputs:
                generated_text = outputs[0].outputs[0].text.strip()

                print(f"  ✅ Success in {inference_time:.2f}s")
                print(f"  📝 Generated: {len(generated_text):,} chars")
                print(f"  📄 Preview: {generated_text[:200]}...")

                results.append({
                    'prompt_id': f"prompt_{i}",
                    'model': model_config['name'],
                    'generated_response': generated_text,
                    'generated_length': len(generated_text),
                    'inference_time': inference_time,
                    'success': True
                })

            else:
                print("  ❌ No output generated")

        except Exception as e:
            print(f"  ❌ Error: {e}")

    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f'universal_vlm_results_{timestamp}.json'

    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)

    # Summary
    successful = [r for r in results if r.get('success', False)]
    print(f"\n📊 SUMMARY:")
    print(f"  Model: {model_config['name']}")
    print(f"  Processed: {len(results)} prompts")
    print(f"  Successful: {len(successful)}")

    if successful:
        avg_length = sum(r['generated_length'] for r in successful) / len(successful)
        avg_time = sum(r['inference_time'] for r in successful) / len(successful)
        print(f"  Average response: {avg_length:,.0f} chars")
        print(f"  Average time: {avg_time:.2f}s")

    print(f"\n💾 Results saved to: {output_file}")
    print(f"🎉 Universal VLM inference completed!")

if __name__ == "__main__":
    main()
