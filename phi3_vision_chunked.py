#!/usr/bin/env python3
"""
Phi-3-Vision Chunked Inference
Process long prompts by breaking them into chunks and combining results
"""

import json
import time
import base64
from io import BytesIO
from PIL import Image
from vllm import LLM, SamplingParams

def load_prompts():
    """Load prompts from JSONL file"""
    prompts = []
    for filename in ['final_image_prompts_cleaned.jsonl', 'final_image_prompts.jsonl']:
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        prompts.append(json.loads(line))
            print(f"✅ Loaded {len(prompts)} prompts from {filename}")
            return prompts
        except FileNotFoundError:
            continue
    return []

def prepare_image(base64_data: str) -> Image.Image:
    """Convert base64 to PIL Image"""
    try:
        if base64_data.startswith('data:'):
            header, base64_content = base64_data.split(',', 1)
        else:
            base64_content = base64_data.strip()

        image_bytes = base64.b64decode(base64_content)
        image = Image.open(BytesIO(image_bytes))

        if image.mode != 'RGB':
            image = image.convert('RGB')

        # Moderate resizing
        max_size = 512
        if max(image.size) > max_size:
            image.thumbnail((max_size, max_size), Image.Resampling.LANCZOS)

        return image
    except Exception as e:
        print(f"Image error: {e}")
        return None

def chunk_text(text: str, max_chars: int = 8000) -> list:
    """Break text into chunks at natural boundaries"""
    if len(text) <= max_chars:
        return [text]

    chunks = []
    current_chunk = ""

    # Split by paragraphs first
    paragraphs = text.split('\n\n')

    for paragraph in paragraphs:
        if len(current_chunk) + len(paragraph) <= max_chars:
            current_chunk += paragraph + '\n\n'
        else:
            if current_chunk:
                chunks.append(current_chunk.strip())
                current_chunk = paragraph + '\n\n'
            else:
                # Paragraph is too long, split by sentences
                sentences = paragraph.split('. ')
                for sentence in sentences:
                    if len(current_chunk) + len(sentence) <= max_chars:
                        current_chunk += sentence + '. '
                    else:
                        if current_chunk:
                            chunks.append(current_chunk.strip())
                        current_chunk = sentence + '. '

    if current_chunk:
        chunks.append(current_chunk.strip())

    return chunks

def run_chunked_inference(llm, image, system_prompt, user_prompt):
    """Run inference on chunks and combine results"""

    # Create chunks
    system_chunks = chunk_text(system_prompt, max_chars=6000)
    user_chunks = chunk_text(user_prompt, max_chars=6000)

    print(f"    📦 Created {len(system_chunks)} system chunks, {len(user_chunks)} user chunks")

    all_responses = []

    # Process each combination of chunks
    for i, sys_chunk in enumerate(system_chunks[:2]):  # Limit to first 2 chunks
        for j, user_chunk in enumerate(user_chunks[:2]):  # Limit to first 2 chunks

            print(f"    🔄 Processing chunk {i+1},{j+1}...")

            # Create prompt for this chunk
            chunk_prompt = f"<|user|>\n<|image_1|>\n{sys_chunk}\n\n{user_chunk}<|end|>\n<|assistant|>\n"

            # Estimate tokens
            estimated_tokens = len(chunk_prompt) // 2.5 + 2000  # Conservative

            if estimated_tokens > 14000:
                print(f"      ⚠️ Chunk still large ({estimated_tokens} tokens), skipping")
                continue

            try:
                # Sampling parameters
                sampling_params = SamplingParams(
                    temperature=0.1,
                    max_tokens=500,  # Shorter responses for chunks
                    stop=["<|end|>", "<|user|>", "<|assistant|>"]
                )

                # Run inference
                outputs = llm.generate(
                    [{
                        "prompt": chunk_prompt,
                        "multi_modal_data": {"image": [image]}
                    }],
                    sampling_params=sampling_params
                )

                if outputs and outputs[0].outputs:
                    chunk_response = outputs[0].outputs[0].text.strip()
                    all_responses.append(f"Chunk {i+1},{j+1}: {chunk_response}")
                    print(f"      ✅ Generated {len(chunk_response)} chars")
                else:
                    print(f"      ❌ No output for chunk {i+1},{j+1}")

            except Exception as e:
                print(f"      ❌ Error in chunk {i+1},{j+1}: {e}")
                continue

    # Combine all responses
    if all_responses:
        combined_response = "\n\n".join(all_responses)

        # Try to create a final summary
        try:
            summary_prompt = f"<|user|>\n<|image_1|>\nBased on these partial analyses, provide a comprehensive JSON summary:\n\n{combined_response[:4000]}<|end|>\n<|assistant|>\n"

            sampling_params = SamplingParams(
                temperature=0.1,
                max_tokens=800,
                stop=["<|end|>", "<|user|>", "<|assistant|>"]
            )

            outputs = llm.generate(
                [{
                    "prompt": summary_prompt,
                    "multi_modal_data": {"image": [image]}
                }],
                sampling_params=sampling_params
            )

            if outputs and outputs[0].outputs:
                final_response = outputs[0].outputs[0].text.strip()
                print(f"    📋 Created final summary: {len(final_response)} chars")
                return final_response

        except Exception as e:
            print(f"    ⚠️ Summary failed: {e}")

        return combined_response

    return "No successful chunk processing"

def main():
    """Chunked Phi-3-Vision inference"""
    print("🧩 Phi-3-Vision Chunked Inference")
    print("="*50)

    # Load prompts
    prompts = load_prompts()
    if not prompts:
        print("❌ No prompts found")
        return

    print(f"📂 Loaded {len(prompts)} prompts")

    # Initialize with moderate settings
    print("🤖 Loading Phi-3-Vision for chunked processing...")

    try:
        llm = LLM(
            model="microsoft/Phi-3-vision-128k-instruct",
            trust_remote_code=True,
            max_model_len=20480,  # 20k tokens for chunks
            gpu_memory_utilization=0.85,
            max_num_seqs=1,
            limit_mm_per_prompt={"image": 1},
            # Note: Paged attention is enabled by default in modern vLLM
        )
        print("✅ Phi-3-Vision loaded for chunked processing")

    except Exception as e:
        print(f"❌ Loading failed: {e}")
        return

    # Process prompts
    results = []

    for i, prompt_data in enumerate(prompts[:3], 1):  # Test first 3
        print(f"\n{'='*15} PROMPT {i} {'='*15}")

        try:
            # Extract data
            system_prompt = prompt_data.get('SystemPrompt', '')
            user_prompt = prompt_data.get('UserPrompt', '')
            image_data_urls = prompt_data.get('Images', [])
            actual_response = prompt_data.get('Response', '')

            print(f"📋 Processing prompt {i}:")
            print(f"  System: {len(system_prompt):,} chars")
            print(f"  User: {len(user_prompt):,} chars")
            print(f"  Images: {len(image_data_urls)}")

            # Process first image
            image = prepare_image(image_data_urls[0]) if image_data_urls else None
            if not image:
                print("  ❌ No valid image")
                continue

            print(f"  ✓ Image: {image.size}")

            # Run chunked inference
            print("  🚀 Running chunked inference...")
            start_time = time.time()

            generated_text = run_chunked_inference(llm, image, system_prompt, user_prompt)

            inference_time = time.time() - start_time

            print(f"  ✅ Completed in {inference_time:.2f}s")
            print(f"  📝 Generated: {len(generated_text)} chars")
            print(f"  📄 Preview: {generated_text[:200]}...")

            # Calculate similarity
            similarity = 0.0
            if actual_response and generated_text:
                import difflib
                similarity = difflib.SequenceMatcher(
                    None, generated_text.lower(), actual_response.lower()
                ).ratio()
                print(f"  🔍 Similarity: {similarity:.3f}")

            results.append({
                'prompt_id': f"prompt_{i}",
                'generated_response': generated_text,
                'actual_response': actual_response,
                'similarity': similarity,
                'inference_time': inference_time,
                'method': 'chunked',
                'success': True
            })

        except Exception as e:
            print(f"  ❌ Error: {e}")
            results.append({
                'prompt_id': f"prompt_{i}",
                'success': False,
                'error': str(e)
            })

    # Save results
    with open('phi3_chunked_results.json', 'w') as f:
        json.dump(results, f, indent=2)

    # Summary
    successful = [r for r in results if r.get('success', False)]
    print(f"\n📊 SUMMARY:")
    print(f"  Processed: {len(results)} prompts")
    print(f"  Successful: {len(successful)}")

    if successful:
        avg_time = sum(r['inference_time'] for r in successful) / len(successful)
        avg_similarity = sum(r.get('similarity', 0) for r in successful) / len(successful)
        print(f"  Average time: {avg_time:.2f}s")
        print(f"  Average similarity: {avg_similarity:.3f}")

    print(f"\n💾 Results saved to: phi3_chunked_results.json")
    print(f"🎉 Chunked inference completed!")

if __name__ == "__main__":
    main()
