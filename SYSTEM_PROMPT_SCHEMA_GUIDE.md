# System Prompt Schema Guide

## 🎯 **Schema is Embedded in System Prompt (Like Phi-3-Vision)**

### **Key Understanding:**
Your JSONL files contain system prompts that already include the complete response schema. The model should follow this schema naturally, just like in `phi3_vision_inference_clean.py`.

## 📋 **How Schema is Included**

### **System Prompt Structure (from your JSONL files):**
```json
{
  "SystemPrompt": "You are a helpful assistant specialized in extracting comprehensive product information from images and text data. Your task is to analyze product information and provide structured data in JSON format according to the detailed schema provided below.\n\nRESPONSE SCHEMA:\n{\n  \"$schema\": \"http://json-schema.org/draft-07/schema#\",\n  \"title\": \"Comprehensive Product Information Schema\",\n  \"type\": \"object\",\n  \"properties\": {\n    \"generalData\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"gtinOnPack\": {\n          \"type\": \"string\",\n          \"description\": \"Global Trade Item Number...\"\n        },\n        ...\n      }\n    },\n    ...\n  }\n}\n\nINSTRUCTIONS:\n- Analyze all provided images and text data\n- Extract information according to the schema\n- Provide complete JSON response\n- Use null for missing information",
  "UserPrompt": "Product details: ...",
  "Images": ["data:image/jpeg;base64,..."],
  "Response": "{ /* expected JSON response */ }"
}
```

### **Schema Components in System Prompt:**
1. **Schema Definition**: Complete JSON schema with all properties
2. **Instructions**: How to analyze and respond
3. **Format Requirements**: JSON structure expectations
4. **Handling Guidelines**: What to do with missing data

## 🔧 **Phi-3-Vision Approach Applied**

### **Phi-3-Vision Pattern:**
```python
# From phi3_vision_inference_clean.py
full_prompt = f"<|user|>\n{image_tokens}\n{cleaned_system}\n\n{cleaned_user}<|end|>\n<|assistant|>\n"
```

### **InternVL3 Adaptation:**
```python
# Same concept, different format
question = f"<image>\n{cleaned_system}\n\n{cleaned_user}"
```

### **Key Principle:**
- **System prompt contains complete schema** → Model follows naturally
- **No additional enforcement needed** → Clean, simple prompts
- **Temperature 0.3** → Consistent but creative responses
- **Model learns from system instructions** → Natural compliance

## 📊 **Example Flow**

### **Input (from JSONL):**
```json
{
  "SystemPrompt": "RESPONSE SCHEMA:\n{\n  \"generalData\": {\n    \"gtinOnPack\": {\"type\": \"string\"},\n    \"brandName\": {\"type\": \"string\"},\n    ...\n  },\n  \"nutritionalInformation\": {...},\n  ...\n}\n\nINSTRUCTIONS:\nAnalyze the product images and provide JSON response following the schema above.",
  "UserPrompt": "What product information can you extract from these images?",
  "Images": ["data:image/jpeg;base64,/9j/4AAQ..."]
}
```

### **InternVL3 Prompt (Phi-3-Vision Style):**
```
<image>
RESPONSE SCHEMA:
{
  "generalData": {
    "gtinOnPack": {"type": "string"},
    "brandName": {"type": "string"},
    ...
  },
  "nutritionalInformation": {...},
  ...
}

INSTRUCTIONS:
Analyze the product images and provide JSON response following the schema above.

What product information can you extract from these images?
```

### **Expected Response:**
```json
{
  "generalData": {
    "gtinOnPack": "013562130627",
    "brandName": "Annie's Homegrown",
    ...
  },
  "nutritionalInformation": {
    "calories": 290,
    "protein": 12,
    ...
  },
  ...
}
```

## 🎯 **Benefits of This Approach**

### **✅ Natural Schema Compliance:**
1. **Schema is in system prompt** → Model sees it directly
2. **No forced instructions** → Clean, natural prompts
3. **Model follows naturally** → Better compliance
4. **Proven approach** → Works well with Phi-3-Vision

### **✅ Consistent with Phi-3-Vision:**
1. **Same prompt structure** → System + User combined
2. **Same temperature** (0.3) → Consistent behavior
3. **Same approach** → Schema in system prompt
4. **Same validation** → Check format compliance

### **✅ Production Benefits:**
1. **Cleaner prompts** → No extra enforcement text
2. **Better model behavior** → Follows instructions naturally
3. **Maintainable code** → Simple, clear structure
4. **Reliable results** → Schema compliance from system prompt

## 📊 **Expected Results**

### **Inference Output:**
```bash
python internvl3_complete_a40.py

🌟 InternVL3-8B Complete Inference for A40 GPU
📋 Schema source: System prompts (like Phi-3-Vision)

=============== PROMPT 1/5 ===============
📝 Question length: 45,234 chars
📋 Schema source: System prompt contains complete schema (like Phi-3-Vision)
📋 Prompt structure: Exact Phi-3-Vision style (system+user combined)

🚀 Running InternVL3 inference on A40...
✅ A40 inference completed in 18.4s

🔍 Validating response format...
📋 Schema source: System prompt (following Phi-3-Vision approach)
✅ Response validation: Valid JSON following schema
✅ Model followed schema from system prompt correctly

📊 A40 SUMMARY:
🤖 Model: InternVL3-8B (Phi-3-Vision style prompts)
📋 Schema source: System prompts (like Phi-3-Vision)
🌡️ Temperature: 0.3 (matching Phi-3-Vision)
📋 Schema compliance: 5/5 (100.0%)
```

## 🔍 **Validation Process**

### **Schema Compliance Check:**
1. **Model generates response** based on system prompt schema
2. **Validate JSON format** → Check if valid JSON
3. **Validate against schema** → Check compliance (optional)
4. **Extract if needed** → Handle mixed responses
5. **Report compliance** → Track success rate

### **Validation Messages:**
```
✅ Model followed schema from system prompt correctly
⚠️ Response validation failed: Invalid JSON format
🔧 Extracted JSON from response
💡 Model should follow schema from system prompt naturally
```

## 🎉 **Summary**

### **✅ Schema in System Prompt:**
- **Complete schema definition** included in system prompts
- **Model follows naturally** without extra enforcement
- **Phi-3-Vision approach** proven to work well
- **Clean, simple prompts** without forced instructions

### **✅ InternVL3 Implementation:**
- **Exact Phi-3-Vision structure** adapted for InternVL3
- **Temperature 0.3** for consistent behavior
- **Combined system+user** prompts
- **Natural schema compliance** from system instructions

### **✅ Expected Outcome:**
- **High schema compliance** (model follows system prompt)
- **Clean responses** (no extra text, just JSON)
- **Consistent behavior** (same as Phi-3-Vision)
- **Production ready** (reliable, maintainable)

**The InternVL3-8B inference now relies entirely on the schema embedded in your system prompts, following the exact same approach as Phi-3-Vision for natural and reliable schema compliance!** 🚀
