# CUDA Error Fix Guide

## 🚨 **Error Analysis**

### **The Problem:**
```
❌ CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call
```

### **Root Causes:**
1. **Dtype mismatches** between model and input tensors
2. **Quantization incompatibility** with flash attention
3. **Memory alignment issues** with 4-bit quantization
4. **Tensor shape/size mismatches**

## 🔧 **Fixes Applied**

### **1. Updated Model Loading (`utils/internvl_model_initializer.py`):**

#### **Safer Quantization Config:**
```python
def create_quantization_config(precision="4bit"):
    if precision == "4bit":
        return BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_compute_dtype=torch.bfloat16,  # Changed from float16
            bnb_4bit_quant_type="nf4",
            bnb_4bit_use_double_quant=True,
        )
```

#### **Disabled Flash Attention for Quantized Models:**
```python
if precision == "4bit" or precision == "8bit":
    model = AutoModel.from_pretrained(
        model_id,
        quantization_config=quantization_config,
        torch_dtype=torch.bfloat16,  # Consistent dtype
        use_flash_attn=False,  # Disabled for quantized models
        attn_implementation="eager"  # Use eager attention
    )
```

### **2. Created Safe Test Script (`test_internvl3_safe.py`):**

#### **CUDA Error Detection:**
```python
# Set environment variables for debugging
os.environ["CUDA_LAUNCH_BLOCKING"] = "1"
os.environ["TORCH_USE_CUDA_DSA"] = "1"

def safe_cuda_operation(func, *args, **kwargs):
    """Safely execute CUDA operations with error handling"""
    try:
        torch.cuda.empty_cache()  # Clear cache before operation
        result = func(*args, **kwargs)
        torch.cuda.synchronize()  # Ensure operation completes
        return result, None
    except Exception as e:
        torch.cuda.empty_cache()  # Clear cache on error
        return None, str(e)
```

#### **Conservative Settings:**
```python
model_precision = "8bit"      # More stable than 4-bit
image_precision = "bfloat16"  # Better numerical stability
max_num = 4                   # Fewer tiles for safety
```

### **3. Enhanced Main Script (`internvl3_inference.py`):**

#### **Safer Default Settings:**
```python
model_precision = "8bit"      # Changed from 4bit
image_precision = "bfloat16"  # Changed from float16
```

#### **GPU Memory Management:**
```python
try:
    # Clear GPU cache before tensor operations
    torch.cuda.empty_cache()
    
    combined_pixel_values = combined_pixel_values.to(torch.bfloat16).to(device)
    
    # Synchronize to catch any CUDA errors early
    torch.cuda.synchronize()
    
except Exception as e:
    print(f"❌ Error moving tensors to GPU: {e}")
    continue
```

## 🚀 **Solution Options**

### **Option 1: Use Safe Test Script (Recommended)**
```bash
python test_internvl3_safe.py
```

**Features:**
- ✅ **Comprehensive error handling**
- ✅ **CUDA debugging enabled**
- ✅ **Conservative settings** (8-bit + bfloat16)
- ✅ **Step-by-step validation**

### **Option 2: Use Updated Main Script**
```bash
python internvl3_inference.py
```

**Features:**
- ✅ **Safer default settings** (8-bit instead of 4-bit)
- ✅ **Enhanced error handling**
- ✅ **Memory management** improvements

### **Option 3: Manual Configuration**
```python
# In internvl3_inference.py, try different combinations:

# Most stable (highest memory)
model_precision = "bfloat16"
image_precision = "bfloat16"

# Balanced (medium memory)
model_precision = "8bit"
image_precision = "bfloat16"

# Experimental (lowest memory, might have issues)
model_precision = "4bit"
image_precision = "float16"
```

## 📊 **Expected Results**

### **Safe Test Script:**
```bash
python test_internvl3_safe.py

🧪 Safe Testing InternVL3-8B Model
🛡️ Enhanced with CUDA error handling and dtype safety
🔢 Testing with model precision: 8bit
🖼️ Testing with image precision: bfloat16

✅ CUDA available: NVIDIA A100-SXM4-80GB
💾 GPU Memory: 80.0GB

🤖 INITIALIZING MODEL
✅ Model initialized successfully
💾 Model device: cuda:0
🔢 Model dtype: torch.uint8

🧪 TEST 1: Pure Text Conversation
Question: Hello, please introduce yourself in one sentence.
Response: Hello! I'm InternVL3, an advanced vision-language model...
✅ Text conversation test successful!

🧪 TEST 2: Simple Image Test
📥 Downloading test image...
✅ Test image downloaded: (1024, 683)
📊 Image processed: torch.Size([5, 3, 448, 448])
🔢 Image dtype: torch.bfloat16
Question: <image>\nDescribe this image briefly.
Response: This image shows a close-up of a vibrant yellow flower...
✅ Image description test successful!

🎉 Safe testing completed!
```

### **Main Script (if test passes):**
```bash
python internvl3_inference.py

🌟 InternVL3-8B Vision-Language Model Inference
🎯 Features: URL removal, No truncation, Safe precision
🔢 Model precision: 8bit (safer than 4-bit)
🖼️ Image precision: bfloat16 (numerical stability)

✅ InternVL3-8B ready for inference
📊 Combined images: torch.Size([10, 3, 448, 448])
🔢 Image tensor dtype: torch.bfloat16
✅ Inference completed in 19.2s
📝 Generated: 2,847 chars
```

## 🔍 **Troubleshooting**

### **If CUDA Errors Persist:**

#### **1. Check Dependencies:**
```bash
pip install bitsandbytes accelerate
pip install torch --upgrade
pip install transformers --upgrade
```

#### **2. Try Different Precision:**
```python
# Most stable (no quantization)
model_precision = "bfloat16"
image_precision = "bfloat16"
```

#### **3. Reduce Memory Usage:**
```python
max_num = 2  # Fewer tiles per image
input_size = 224  # Smaller image size
```

#### **4. Enable CUDA Debugging:**
```bash
export CUDA_LAUNCH_BLOCKING=1
export TORCH_USE_CUDA_DSA=1
python test_internvl3_safe.py
```

#### **5. Check GPU Memory:**
```python
import torch
print(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")
print(f"Available: {torch.cuda.memory_reserved() / 1024**3:.1f}GB")
```

## 🎯 **Recommended Workflow**

### **Step 1: Test with Safe Script**
```bash
python test_internvl3_safe.py
```

### **Step 2: If Test Passes, Run Main Script**
```bash
python internvl3_inference.py
```

### **Step 3: If Issues Persist, Try Conservative Settings**
```python
# In internvl3_inference.py
model_precision = "bfloat16"  # No quantization
image_precision = "bfloat16"  # Highest stability
```

## 🎉 **Summary**

### **✅ Fixes Applied:**
1. **Changed default to 8-bit** (more stable than 4-bit)
2. **Disabled flash attention** for quantized models
3. **Used bfloat16** for better numerical stability
4. **Added comprehensive error handling**
5. **Created safe test script** with CUDA debugging

### **✅ Expected Outcome:**
- **No more CUDA assert errors**
- **Stable 8-bit quantization** (~8GB VRAM)
- **Better numerical stability** with bfloat16
- **Comprehensive error reporting** for debugging

### **✅ Fallback Options:**
- **Safe test script** for validation
- **Conservative settings** if issues persist
- **No quantization mode** for maximum stability

**The updated scripts should now work without CUDA errors while maintaining good memory efficiency!** 🚀
