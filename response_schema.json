{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Comprehensive Product Information Schema", "description": "A comprehensive schema that combines all product information including general data, nutritional information, serving sizes, ingredients, allergens, and product claims. This schema represents a complete view of product attributes, certifications, and nutritional data in a structured format.", "type": "object", "properties": {"generalData": {"type": "object", "description": "General product identification and classification information", "properties": {"gtinOnPack": {"type": "string", "description": "Global Trade Item Number (GTIN) as it appears on the product packaging. This is the standard barcode number used for product identification. Example: '013562130627' for a specific product variant."}, "gtin14": {"type": "string", "description": "14-digit GTIN format with leading zeros. This is the standardized format for GTIN numbers, always 14 digits long. Example: '00013562130627' (same as GTIN On Pack but with leading zeros to make it 14 digits)."}, "upc12": {"type": "string", "description": "12-digit Universal Product Code (UPC) format without check digit. This is the standard retail barcode format used in North America. Example: '001356213062' (first digit is typically 0 for standard UPC)."}, "brandOwner": {"type": "string", "description": "NielsenIQ Brand Owner/Manufacturer name. This is the legal entity that owns the brand. Example: 'ANNIE'S HOMEGROWN INC.' for <PERSON>'s products."}, "brandName": {"type": "string", "description": "NielsenIQ Brand name including parent company. This combines the brand name with its parent company for clarity. Example: 'ANNIE'S (ANNIE'S HOMEGROWN INC.)' shows both the consumer-facing brand and its corporate owner."}, "department": {"type": "string", "description": "NielsenIQ Department category - the highest level of product classification. Example: 'FROZEN' for frozen food products, 'DAIRY' for dairy products, 'BAKERY' for bakery items."}, "superCategory": {"type": "string", "description": "NielsenIQ Super Category classification - the second level of product classification. Example: 'PREPARED FOODS' for ready-to-eat items, 'BEVERAGES' for drinks, 'SNACKS' for snack products."}, "category": {"type": "string", "description": "NielsenIQ Category classification - a more specific product grouping. Example: 'WAFFLE' for waffle products, 'YOGURT' for yogurt products, 'CHIPS' for potato chips."}, "subCategory": {"type": "string", "description": "NielsenIQ Sub Category classification - a detailed product grouping. Example: 'REGULAR' for standard products, 'LIGHT' for reduced-fat items, 'ORGANIC' for organic products."}, "segment": {"type": "string", "description": "NielsenIQ Segment classification - the most specific product grouping. Example: 'REGULAR' for standard products, 'FLAVORED' for flavored variants, 'WHOLE GRAIN' for whole grain products."}, "productTitle": {"type": "string", "description": "Product title from identifying header information. This is the full product name as it appears in the system. Example: 'BIRTHDAY CAKE ORGANIC WAFFLES' or 'STRAWBERRY SHORTCAKE ORGANIC WAFFLES'."}, "variant": {"type": "string", "description": "Product variant or flavor information. This specifies the specific version or flavor of the product. Example: 'BIRTHDAY CAKE' or 'STRAWBERRY SHORTCAKE' for different waffle flavors."}, "netWeight1Value": {"type": "number", "description": "Primary net weight value of the product. This is the main weight measurement. Example: 9.8 for 9.8 ounces, 280 for 280 grams."}, "netWeight1UOM": {"type": "string", "description": "Primary net weight unit of measurement. This specifies the unit used for the primary weight. Example: 'oz' for ounces, 'g' for grams, 'lb' for pounds."}, "netWeight2Value": {"type": "number", "description": "Secondary net weight value of the product. This is an alternative weight measurement, often in a different unit. Example: 280 for 280 grams when primary is in ounces."}, "netWeight2UOM": {"type": "string", "description": "Secondary net weight unit of measurement. This specifies the unit used for the secondary weight. Example: 'g' for grams when primary is in ounces, or 'ml' for milliliters for liquids."}, "unitsPerPack": {"type": "integer", "description": "Number of individual packaging units per pack. This indicates how many individual items are in the package. Example: 8 for a pack of 8 waffles, 6 for a 6-pack of yogurt."}, "unitsPerPackDescriptor": {"type": "string", "description": "Description of the individual packaging units. This specifies what each unit represents. Example: 'WAFFLES' for waffle products, 'BARS' for snack bars, 'POUCHES' for individual pouches."}, "storage": {"type": "string", "description": "Product storage requirements. This indicates how the product should be stored. Example: 'Frozen' for frozen products, 'Refrigerated' for cold items, 'Room Temperature' for shelf-stable products."}, "numberOfIngredients": {"type": "integer", "description": "Total number of ingredients in the product, including all nested ingredients. This should be a count of all individual ingredients at all levels of nesting."}}, "required": ["gtinOnPack", "gtin14", "upc12", "brandOwner", "brandName", "department", "superCategory", "category", "subCategory", "segment", "productTitle", "variant", "netWeight1Value", "netWeight1UOM", "netWeight2Value", "netWeight2UOM", "unitsPerPack", "unitsPerPackDescriptor", "storage", "numberOfIngredients"]}, "servingSize": {"type": "object", "description": "Product serving size information", "properties": {"servingSize": {"type": "number", "description": "Primary serving size value. This represents the standard serving amount for the product. For example: 70 for a 70g serving, 1 for a 1 cup serving, or 2 for a 2-piece serving."}, "servingSizeUnit": {"type": "string", "description": "Unit of measure for the primary serving size. Common units include: 'g' for grams, 'oz' for ounces, 'ml' for milliliters, 'cup' for cups, or 'piece' for individual items. Example: 'g' for a 70g serving."}, "servesPerPack": {"type": "integer", "description": "Number of servings per package. This indicates how many standard servings are contained in the entire package. For example: 4 means the package contains 4 servings, 6 means 6 servings, etc."}, "servingDescription": {"type": "string", "description": "Textual description of the serving size that provides context about how the serving size should be interpreted. Examples: '2 Waffles', '1/2 cup (120g)', '1 bar (40g)', '1 pouch (28g)'."}, "servingSize2": {"type": ["number", "null"], "description": "Optional secondary serving size value. This is used when a product provides an alternative serving size measurement. For example: if primary is in grams (70g), secondary might be in pieces (2 pieces). Can be null if no secondary serving size is provided."}, "servingSize2Unit": {"type": ["string", "null"], "description": "Unit of measure for the secondary serving size. Used in conjunction with servingSize2. Examples: 'piece' for number of items, 'oz' for ounces, 'ml' for milliliters. Can be null if no secondary serving size is provided."}}, "required": ["servingSize", "servingSizeUnit", "servesPerPack", "servingDescription"]}, "nutritionalInformation": {"type": "object", "description": "Nutritional information divided into stated (explicitly mentioned) and qualified (AI-inferred) values", "properties": {"stated": {"type": "object", "description": "Nutritional information as explicitly stated on the product packaging", "properties": {"totalAmount": {"type": "number", "description": "The total amount that all nutrient values are measured against as stated on packaging. For example, if nutrients are measured per 100g, this would be 100. Only extract if this is mentioned in the input data. Otherwise, set to null."}, "totalAmountUOM": {"type": "string", "description": "Unit of measurement for the total amount as stated on packaging. Common units include: 'g' for grams, 'oz' for ounces, 'ml' for milliliters. Only extract if this is mentioned in the input data. Otherwise, set to null."}, "source": {"type": "string", "enum": ["Amazon Images", "Product Scrape", "ScrapingQualificationAI"], "description": "Source of the stated nutritional information"}, "macronutrients": {"type": "array", "description": "Primary nutrients essential in larger quantities as stated on packaging", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the nutrient as stated. Like 'Protein', 'Carbohydrates', 'Fat', 'Fibre', 'Sugar', 'Salt', etc."}, "amount": {"type": "number", "description": "Amount of the nutrient as stated on packaging. Only extract if this is mentioned in the input data. Otherwise, set to null."}, "totalAmount": {"type": "number", "description": "The total amount that the nutrient value is measured against as stated. For example, if the nutrient is 10g out of 100g, this would be 100. Only extract if this is mentioned in the input data. Otherwise, set to null."}, "UOM": {"type": "string", "description": "Unit of measurement for the nutrient as stated, for example 'g' or 'mg' or 'oz'. Only extract if this is mentioned in the input data. Otherwise, set to null."}, "dailyValue": {"type": "number", "description": "Percentage of recommended daily intake as stated. Only extract if this is mentioned in the input data. Otherwise, set to null."}, "source": {"type": "string", "enum": ["Amazon Images", "Product Scrape", "ScrapingQualificationAI"], "description": "Source of this stated nutrient information"}}, "required": ["name", "amount", "totalAmount", "UOM", "dailyValue", "source"]}}, "micronutrients": {"type": "array", "description": "Essential nutrients required in smaller quantities as stated on packaging", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the nutrient as stated. Like 'Vitamin A', 'Vitamin C', 'Calcium', 'Iron', 'Zinc', etc."}, "amount": {"type": "number", "description": "Amount of the nutrient as stated on packaging. Only extract if this is mentioned in the input data. Otherwise, set to null."}, "totalAmount": {"type": "number", "description": "The total amount that the nutrient value is measured against as stated. For example, if the nutrient is 10mg out of 100g, this would be 100. Only extract if this is mentioned in the input data. Otherwise, set to null."}, "UOM": {"type": "string", "description": "Unit of measurement for the nutrient as stated, for example 'g' or 'mg' or 'oz'. Only extract if this is mentioned in the input data. Otherwise, set to null."}, "dailyValue": {"type": "number", "description": "Percentage of recommended daily intake as stated. Only extract if this is mentioned in the input data. Otherwise, set to null."}, "source": {"type": "string", "enum": ["Amazon Images", "Product Scrape", "ScrapingQualificationAI"], "description": "Source of this stated nutrient information"}}, "required": ["name", "amount", "totalAmount", "UOM", "dailyValue", "source"]}}}, "required": ["totalAmount", "totalAmountUOM", "source", "macronutrients", "micronutrients"]}, "qualified": {"type": "object", "description": "Nutritional information inferred or calculated by AI based on ingredients and product knowledge. If this information is not present in the data provided, make sure to populate based on your understanding of the data.", "properties": {"totalAmount": {"type": "number", "description": "The total amount that all nutrient values are measured against as inferred by AI. For example, if nutrients are estimated per 100g, this would be 100. If this information is not present in the data provided, make sure to populate based on your understanding of the data."}, "totalAmountUOM": {"type": "string", "description": "Unit of measurement for the total amount as inferred by AI. Common units include: 'g' for grams, 'oz' for ounces, 'ml' for milliliters. If this information is not present in the data provided, make sure to populate based on your understanding of the data."}, "source": {"type": "string", "enum": ["Amazon Images", "Product Scrape", "ScrapingQualificationAI"], "description": "Source of the qualified nutritional information (typically 'ScrapingQualificationAI' for inferred values)"}, "macronutrients": {"type": "array", "description": "Primary nutrients essential in larger quantities as inferred by AI. If this information is not present in the data provided, make sure to populate based on your understanding of the data.", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the nutrient as inferred. Like 'Protein', 'Carbohydrates', 'Fat', 'Fibre', 'Sugar', 'Salt', etc."}, "amount": {"type": "number", "description": "Amount of the nutrient as estimated by AI based on ingredients and product knowledge. If this information is not present in the data provided, make sure to populate based on your understanding of the data."}, "totalAmount": {"type": "number", "description": "The total amount that the nutrient value is measured against as inferred. For example, if the nutrient is estimated as 10g out of 100g, this would be 100. If this information is not present in the data provided, make sure to populate based on your understanding of the data."}, "UOM": {"type": "string", "description": "Unit of measurement for the nutrient as inferred, for example 'g' or 'mg' or 'oz'. If this information is not present in the data provided, make sure to populate based on your understanding of the data."}, "dailyValue": {"type": "number", "description": "Percentage of recommended daily intake as calculated by AI. If this information is not present in the data provided, make sure to populate based on your understanding of the data."}, "source": {"type": "string", "enum": ["Amazon Images", "Product Scrape", "ScrapingQualificationAI"], "description": "Source of this qualified nutrient information (typically 'ScrapingQualificationAI' for inferred values)"}}, "required": ["name", "amount", "totalAmount", "UOM", "dailyValue", "source"]}}, "micronutrients": {"type": "array", "description": "Essential nutrients required in smaller quantities as inferred by AI. If this information is not present in the data provided, make sure to populate based on your understanding of the data.", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the nutrient as inferred. Like 'Vitamin A', 'Vitamin C', 'Calcium', 'Iron', 'Zinc', etc."}, "amount": {"type": "number", "description": "Amount of the nutrient as estimated by AI based on ingredients and product knowledge. If this information is not present in the data provided, make sure to populate based on your understanding of the data."}, "totalAmount": {"type": "number", "description": "The total amount that the nutrient value is measured against as inferred. For example, if the nutrient is estimated as 10mg out of 100g, this would be 100. If this information is not present in the data provided, make sure to populate based on your understanding of the data."}, "UOM": {"type": "string", "description": "Unit of measurement for the nutrient as inferred, for example 'g' or 'mg' or 'oz'. If this information is not present in the data provided, make sure to populate based on your understanding of the data."}, "dailyValue": {"type": "number", "description": "Percentage of recommended daily intake as calculated by AI. If this information is not present in the data provided, make sure to populate based on your understanding of the data."}, "source": {"type": "string", "enum": ["Amazon Images", "Product Scrape", "ScrapingQualificationAI"], "description": "Source of this qualified nutrient information (typically 'ScrapingQualificationAI' for inferred values)"}}, "required": ["name", "amount", "totalAmount", "UOM", "dailyValue", "source"]}}}, "required": ["totalAmount", "totalAmountUOM", "source", "macronutrients", "micronutrients"]}}, "required": ["stated", "qualified"]}, "ingredients": {"type": "object", "description": "Ingredient information divided into stated (explicitly mentioned) and qualified (AI-inferred) values", "properties": {"stated": {"type": "object", "description": "Ingredients as explicitly stated on the product packaging", "properties": {"source": {"type": "string", "enum": ["Amazon Images", "Product Scrape", "ScrapingQualificationAI"], "description": "Source of the stated ingredient information"}, "ingredientList": {"type": "array", "description": "List of ingredients as explicitly stated on packaging. If you find the ingredients to be nested, extract the nested ingredients as well. For example, if the text is 'Chocolate (sugar, whole milk, cocoa butter, chocolate liquor, soy lecithin 'an emulsifier' and pure vanilla)' then 'Chocolate' is the parent ingredient and 'sugar', 'whole milk', 'cocoa butter', 'chocolate liquor', 'soy lecithin 'an emulsifier' and 'pure vanilla' are the nested ingredients. All the properties of ingredients like amount, amountUOM, dietary_preference, allergens, religious_labels, food_safety_labels, sustainability_labels, etc. should be extracted for the parent as well as the nested ingredients.", "items": {"$ref": "#/definitions/Ingredient"}}}, "required": ["source", "ingredientList"]}, "qualified": {"type": "object", "description": "Ingredients as analyzed and inferred by AI based on product knowledge and additional research. If this information is not present in the data provided, make sure to populate based on your understanding of the data.", "properties": {"source": {"type": "string", "enum": ["Amazon Images", "Product Scrape", "ScrapingQualificationAI"], "description": "Source of the qualified ingredient information (typically 'ScrapingQualificationAI' for inferred values)"}, "ingredientList": {"type": "array", "description": "List of ingredients as analyzed by AI, including potential hidden ingredients, processing aids, or ingredients that may not be explicitly listed but are commonly found in similar products. If this information is not present in the data provided, make sure to populate based on your understanding of the data.", "items": {"$ref": "#/definitions/Ingredient"}}}, "required": ["source", "ingredientList"]}}, "required": ["stated", "qualified"]}, "claims": {"type": "object", "description": "Product claims and marketing information", "properties": {"certifications": {"type": "array", "description": "Product certifications and approvals from recognized organizations. Examples include: ['USDA Organic', 'Non-GMO Project Verified', 'Gluten-Free Certified', 'Kosher', 'Vegan Certified']. These certifications indicate that the product meets specific standards set by certifying bodies.", "items": {"type": "string"}}, "nutritionalClaims": {"type": "array", "description": "Nutritional claims and benefits stated on the product. Examples include: ['Good Source of Fiber', 'Low Sodium', 'High in Protein', 'No Added Sugar', 'Reduced Fat']. These claims must comply with regulatory guidelines for nutritional labeling.", "items": {"type": "string"}}, "ingredientClaims": {"type": "array", "description": "Claims about ingredients and their sourcing. Examples include: ['Made with Real Fruit', '100% Whole Grain', 'No Artificial Colors', 'No Preservatives', 'Locally Sourced Ingredients']. These claims highlight specific aspects of the ingredients used.", "items": {"type": "string"}}, "preparation": {"type": "object", "description": "Product preparation instructions and storage requirements", "properties": {"storage": {"type": "string", "description": "Storage instructions for maintaining product quality. Examples: 'Keep frozen until ready to use', 'Store in a cool, dry place', 'Refrigerate after opening'. These instructions are crucial for food safety and product quality."}, "heatingInstructions": {"type": "object", "description": "Detailed heating/cooking instructions for different preparation methods", "properties": {"toaster": {"type": "string", "description": "Toaster preparation instructions. Example: 'Toast on medium setting for 2-3 minutes until golden brown'. These instructions are specific to toaster preparation."}, "oven": {"type": "string", "description": "Oven preparation instructions. Example: 'Preheat oven to 375°F, bake for 8-10 minutes until crispy'. These instructions are specific to oven preparation."}}, "required": ["toaster", "oven"]}}, "required": ["storage", "heatingInstructions"]}, "sustainability": {"type": "object", "description": "Environmental and sustainability claims about the product and its packaging", "properties": {"packaging": {"type": "array", "description": "Information about packaging materials and recyclability. Examples include: ['100% Recyclable Packaging', 'Made from 30% Post-Consumer Recycled Material', 'Compostable Packaging']. These claims indicate the environmental attributes of the packaging.", "items": {"type": "string"}}, "environmentalClaims": {"type": "array", "description": "Environmental impact claims about the product and its production. Examples include: ['Carbon Neutral', 'Sustainably Sourced', 'Water Conservation Practices', 'Reduced Carbon Footprint']. These claims highlight the product's environmental benefits.", "items": {"type": "string"}}}, "required": ["packaging", "environmentalClaims"]}, "contact": {"type": "object", "description": "Company contact information for customer inquiries and support", "properties": {"website": {"type": "string", "description": "Company website URL for additional information. Example: 'https://www.annies.com'. This is the primary online resource for product information."}, "phone": {"type": "string", "description": "Customer service phone number for inquiries. Example: '**************'. This provides direct access to customer support."}}, "required": ["website", "phone"]}}, "required": ["certifications", "nutritionalClaims", "ingredientClaims", "preparation", "sustainability", "contact"]}, "NPI 2.0 Food Packages - Allergens & Intolerances": {"type": "object", "description": "Allergen and intolerance information", "properties": {"fdaRegulatedAllergens": {"type": "string", "description": "NPI 2.0 Food Packages - Allergens & Intolerances - PI All FDA Regulated Allergens. This field lists all allergens that are regulated by the FDA and present in the product. Example: 'Contains: Milk, Eggs, Wheat, Soy' or 'May contain traces of: Tree Nuts, Peanuts'."}, "caseinQualified": {"type": "string", "description": "NPI 2.0 Food Packages - Allergens & Intolerances - PI Casein Qualified. Indicates if the product contains casein or casein derivatives. Extract based on known facts about the product even if not explicitly stated. Do not let this be null."}, "caseinStated": {"type": "string", "description": "NPI 2.0 Food Packages - Allergens & Intolerances - PI Casein Stated. Explicit statement about casein content on the product label. Example: 'Made with Casein' or 'No Casein Added'."}, "coconutQualified": {"type": "string", "description": "NPI 2.0 Food Packages - Allergens & Intolerances - PI Coconut Qualified. Indicates if the product contains coconut or coconut derivatives. Extract based on known facts about the product even if not explicitly stated. Do not let this be null."}, "coconutStated": {"type": "string", "description": "NPI 2.0 Food Packages - Allergens & Intolerances - PI Coconut Stated. Explicit statement about coconut content on the product label. Example: 'Made with Coconut' or 'No Coconut Added'."}, "cornQualified": {"type": "string", "description": "NPI 2.0 Food Packages - Allergens & Intolerances - PI Corn Qualified. Indicates if the product contains corn or corn derivatives. Extract based on known facts about the product even if not explicitly stated. Do not let this be null."}, "cornStated": {"type": "string", "description": "NPI 2.0 Food Packages - Allergens & Intolerances - PI Corn Stated. Explicit statement about corn content on the product label. Example: 'Made with Corn' or 'No Corn Added'."}, "dairyLevelStated": {"type": "string", "description": "NPI 2.0 Food Packages - Allergens & Intolerances - PI Dairy Level Stated. Specifies the level of dairy content in the product. Example: 'Contains 2% Milk' or 'Lactose-Free'."}, "dairyQualified": {"type": "string", "description": "NPI 2.0 Food Packages - Allergens & Intolerances - PI Dairy Qualified. Indicates if the product contains dairy or dairy derivatives. Extract based on known facts about the product even if not explicitly stated. Do not let this be null."}, "dairyStated": {"type": "string", "description": "NPI 2.0 Food Packages - Allergens & Intolerances - PI Dairy Stated. Explicit statement about dairy content on the product label. Example: 'Made with Real Dairy' or 'No Dairy Added'."}, "eggLevelStated": {"type": "string", "description": "NPI 2.0 Food Packages - Allergens & Intolerances - PI Egg Level Stated. Specifies the level of egg content in the product. Example: 'Contains Whole Eggs' or 'Egg-Free'."}, "eggQualified": {"type": "string", "description": "NPI 2.0 Food Packages - Allergens & Intolerances - PI Egg Qualified. Indicates if the product contains eggs or egg derivatives. Extract based on known facts about the product even if not explicitly stated. Do not let this be null."}, "eggStated": {"type": "string", "description": "NPI 2.0 Food Packages - Allergens & Intolerances - PI Egg Stated. Explicit statement about egg content on the product label. Example: 'Made with Real Eggs' or 'No Eggs Added'."}, "falcpaCommonAllergensQualified": {"type": "string", "description": "NPI 2.0 Food Packages - Allergens & Intolerances - PI FALCPA Common Allergens Qualified. Indicates if the product contains any of the major food allergens as defined by FALCPA. Extract based on known facts about the product even if not explicitly stated. Do not let this be null."}, "falcpaCommonAllergensStated": {"type": "string", "description": "NPI 2.0 Food Packages - Allergens & Intolerances - PI FALCPA Common Allergens Stated. Explicit statement about FALCPA-defined allergen content on the product label. Example: 'Contains: Milk, Eggs, Fish, Shellfish, Tree Nuts, Peanuts, Wheat, Soybeans'."}, "fishLevelStated": {"type": "string", "description": "NPI 2.0 Food Packages - Allergens & Intolerances - PI Fish Level Stated. Specifies the level of fish content in the product. Example: 'Contains Fish' or 'Fish-Free'."}, "fishQualified": {"type": "string", "description": "NPI 2.0 Food Packages - Allergens & Intolerances - PI Fish Qualified. Indicates if the product contains fish or fish derivatives. Extract based on known facts about the product even if not explicitly stated. Do not let this be null."}, "fishStated": {"type": "string", "description": "NPI 2.0 Food Packages - Allergens & Intolerances - PI Fish Stated. Explicit statement about fish content on the product label. Example: 'Made with Real Fish' or 'No Fish Added'."}, "glutenLevelStated": {"type": "string", "description": "NPI 2.0 Food Packages - Allergens & Intolerances - PI Gluten Level Stated. Specifies the level of gluten content in the product. Example: 'Gluten-Free' or 'Contains Gluten'."}, "glutenQualified": {"type": "string", "description": "NPI 2.0 Food Packages - Allergens & Intolerances - PI Gluten Qualified. Indicates if the product contains gluten or gluten derivatives. Extract based on known facts about the product even if not explicitly stated. Do not let this be null."}}}, "cleanLabel": {"type": "object", "description": "Clean label and ingredient quality claims", "properties": {"artisanalStated": {"type": "string", "description": "NPI 2.0 Food Packages - Artisan/Premium - PI Artisanal Stated. Explicit statement about artisanal production methods. Example: 'Handcrafted' or 'Artisan Made'."}, "coldPressedIngredientsStated": {"type": "string", "description": "NPI 2.0 Food Packages - Artisan/Premium - PI Cold Pressed Ingredients Stated. Explicit statement about cold-pressed ingredients. Example: 'Made with Cold-Pressed Oils' or 'Cold-Pressed Juices'."}, "craftStated": {"type": "string", "description": "NPI 2.0 Food Packages - Artisan/Premium - PI Craft Stated. Explicit statement about craft production methods. Example: 'Craft Brewed' or 'Small Batch Crafted'."}, "gourmetStated": {"type": "string", "description": "NPI 2.0 Food Packages - Artisan/Premium - PI Gourmet Stated. Explicit statement about gourmet quality. Example: 'Gourmet Quality' or 'Premium Gourmet'."}, "localStated": {"type": "string", "description": "NPI 2.0 Food Packages - Artisan/Premium - PI Local Stated. Explicit statement about local sourcing. Example: 'Locally Sourced' or 'Made with Local Ingredients'."}, "madeInUsaStated": {"type": "string", "description": "NPI 2.0 Food Packages - Artisan/Premium - PI Made In USA Stated. Explicit statement about USA origin. Example: 'Made in USA' or 'Product of USA'."}, "premiumStated": {"type": "string", "description": "NPI 2.0 Food Packages - Artisan/Premium - PI Premium Stated. Explicit statement about premium quality. Example: 'Premium Quality' or 'Premium Ingredients'."}, "smallBatchStated": {"type": "string", "description": "NPI 2.0 Food Packages - Artisan/Premium - PI Small Batch Stated. Explicit statement about small batch production. Example: 'Small Batch Produced' or 'Crafted in Small Batches'."}, "animalByProductQualified": {"type": "string", "description": "NPI 2.0 Food Packages - Clean Label Trends - PI Animal By Product Qualified. Indicates if the product contains animal by-products. Extract based on known facts about the product even if not explicitly stated."}, "animalByProductStated": {"type": "string", "description": "NPI 2.0 Food Packages - Clean Label Trends - PI Animal By Product Stated. Explicit statement about animal by-product content. Example: 'Made with Animal By-Products' or 'No Animal By-Products Added'."}, "antibioticsQualified": {"type": "string", "description": "NPI 2.0 Food Packages - Clean Label Trends - PI Antibiotics Qualified. Indicates if the product contains ingredients from animals treated with antibiotics. Extract based on known facts about the product even if not explicitly stated."}, "antibioticsStated": {"type": "string", "description": "NPI 2.0 Food Packages - Clean Label Trends - PI Antibiotics Stated. Explicit statement about antibiotic use. Example: 'No Antibiotics Ever' or 'Raised Without Antibiotics'."}, "artificialColorsQualified": {"type": "string", "description": "NPI 2.0 Food Packages - Clean Label Trends - PI Artificial Colors Qualified. Indicates if the product contains artificial colors. Extract based on known facts about the product even if not explicitly stated."}, "artificialColorsStated": {"type": "string", "description": "NPI 2.0 Food Packages - Clean Label Trends - PI Artificial Colors Stated. Explicit statement about artificial color content. Example: 'No Artificial Colors Added' or 'Colored with Artificial Dyes'."}, "artificialFlavorsQualified": {"type": "string", "description": "NPI 2.0 Food Packages - Clean Label Trends - PI Artificial Flavors Qualified. Indicates if the product contains artificial flavors. Extract based on known facts about the product even if not explicitly stated."}, "artificialFlavorsStated": {"type": "string", "description": "NPI 2.0 Food Packages - Clean Label Trends - PI Artificial Flavors Stated. Explicit statement about artificial flavor content. Example: 'No Artificial Flavors Added' or 'Flavored with Artificial Ingredients'."}, "artificialIngredientsQualified": {"type": "string", "description": "NPI 2.0 Food Packages - Clean Label Trends - PI Artificial Ingredients Qualified. Indicates if the product contains any artificial ingredients. Extract based on known facts about the product even if not explicitly stated."}, "artificialPreservativesQualified": {"type": "string", "description": "NPI 2.0 Food Packages - Clean Label Trends - PI Artificial Preservatives Qualified. Indicates if the product contains artificial preservatives. Extract based on known facts about the product even if not explicitly stated."}, "artificialPreservativesStated": {"type": "string", "description": "NPI 2.0 Food Packages - Clean Label Trends - PI Artificial Preservatives Stated. Explicit statement about artificial preservative content. Example: 'No Artificial Preservatives Added' or 'Preserved with Artificial Ingredients'."}, "artificialSweetenersQualified": {"type": "string", "description": "NPI 2.0 Food Packages - Clean Label Trends - PI Artificial Sweeteners Qualified. Indicates if the product contains artificial sweeteners. Extract based on known facts about the product even if not explicitly stated."}, "artificialSweetenersStated": {"type": "string", "description": "NPI 2.0 Food Packages - Clean Label Trends - PI Artificial Sweeteners Stated. Explicit statement about artificial sweetener content. Example: 'No Artificial Sweeteners Added' or 'Sweetened with Artificial Sweeteners'."}, "countOfIngredientsQualified": {"type": "string", "description": "NPI 2.0 Food Packages - Clean Label Trends - PI Count Of Ingredients Qualified. Indicates the number of ingredients in the product. Extract based on known facts about the product even if not explicitly stated."}, "gmoPresenceQualified": {"type": "string", "description": "NPI 2.0 Food Packages - Clean Label Trends - PI GMO Presence Qualified. Indicates if the product contains genetically modified organisms. Extract based on known facts about the product even if not explicitly stated."}, "gmoPresenceStated": {"type": "string", "description": "NPI 2.0 Food Packages - Clean Label Trends - PI GMO Presence Stated. Explicit statement about GMO content. Example: 'Non-GMO Project Verified' or 'Made with GMO Ingredients'."}, "highFructoseCornSyrupQualified": {"type": "string", "description": "NPI 2.0 Food Packages - Clean Label Trends - PI High Fructose Corn Syrup Qualified. Indicates if the product contains high fructose corn syrup. Extract based on known facts about the product even if not explicitly stated."}, "highFructoseCornSyrupStated": {"type": "string", "description": "NPI 2.0 Food Packages - Clean Label Trends - PI High Fructose Corn Syrup Stated. Explicit statement about high fructose corn syrup content. Example: 'No High Fructose Corn Syrup Added' or 'Sweetened with High Fructose Corn Syrup'."}, "hormonesQualified": {"type": "string", "description": "NPI 2.0 Food Packages - Clean Label Trends - PI Hormones Qualified. Indicates if the product contains ingredients from animals treated with hormones. Extract based on known facts about the product even if not explicitly stated."}, "hormonesStated": {"type": "string", "description": "NPI 2.0 Food Packages - Clean Label Trends - PI Hormones Stated. Explicit statement about hormone use. Example: 'No Hormones Added' or 'Raised Without Added Hormones'."}, "naturalColorsQualified": {"type": "string", "description": "NPI 2.0 Food Packages - Clean Label Trends - PI Natural Colors Qualified. Indicates if the product contains natural colors. Extract based on known facts about the product even if not explicitly stated."}, "naturalColorsStated": {"type": "string", "description": "NPI 2.0 Food Packages - Clean Label Trends - PI Natural Colors Stated. Explicit statement about natural color content. Example: 'Colored with Natural Ingredients' or 'Naturally Colored with Fruit and Vegetable Extracts'."}, "naturalFlavorsQualified": {"type": "string", "description": "NPI 2.0 Food Packages - Clean Label Trends - PI Natural Flavors Qualified. Indicates if the product contains natural flavors. Extract based on known facts about the product even if not explicitly stated."}, "naturalFlavorsStated": {"type": "string", "description": "NPI 2.0 Food Packages - Clean Label Trends - PI Natural Flavors Stated. Explicit statement about natural flavor content. Example: 'Flavored with Natural Ingredients' or 'Naturally Flavored with Real Fruit'."}, "naturalPreservativesQualified": {"type": "string", "description": "NPI 2.0 Food Packages - Clean Label Trends - PI Natural Preservatives Qualified. Indicates if the product contains natural preservatives. Extract based on known facts about the product even if not explicitly stated."}, "naturalPreservativesStated": {"type": "string", "description": "NPI 2.0 Food Packages - Clean Label Trends - PI Natural Preservatives Stated. Explicit statement about natural preservative content. Example: 'Preserved with Natural Ingredients' or 'Naturally Preserved with Vitamin E'."}, "naturalSweetenersQualified": {"type": "string", "description": "NPI 2.0 Food Packages - Clean Label Trends - PI Natural Sweeteners Qualified. Indicates if the product contains natural sweeteners. Extract based on known facts about the product even if not explicitly stated."}, "naturalSweetenersStated": {"type": "string", "description": "NPI 2.0 Food Packages - Clean Label Trends - PI Natural Sweeteners Stated. Explicit statement about natural sweetener content. Example: 'Sweetened with Natural Ingredients' or 'Naturally Sweetened with Honey'."}, "preservativesQualified": {"type": "string", "description": "NPI 2.0 Food Packages - Clean Label Trends - PI Preservatives Qualified. Indicates if the product contains any preservatives. Extract based on known facts about the product even if not explicitly stated."}, "preservativesStated": {"type": "string", "description": "NPI 2.0 Food Packages - Clean Label Trends - PI Preservatives Stated. Explicit statement about preservative content. Example: 'No Preservatives Added' or 'Preserved to Maintain Freshness'."}, "rbstQualified": {"type": "string", "description": "NPI 2.0 Food Packages - Clean Label Trends - PI RBST Qualified. Indicates if the product contains ingredients from cows treated with rBST. Extract based on known facts about the product even if not explicitly stated."}, "rbstStated": {"type": "string", "description": "NPI 2.0 Food Packages - Clean Label Trends - PI RBST Stated. Explicit statement about rBST content. Example: 'No rBST Added' or 'From Cows Not Treated with rBST'."}, "recognizableIngredientsQualified": {"type": "string", "description": "NPI 2.0 Food Packages - Clean Label Trends - PI Recognizable Ingredients Qualified. Indicates if the product contains easily recognizable ingredients. Extract based on known facts about the product even if not explicitly stated."}, "sugarAlcoholsQualified": {"type": "string", "description": "NPI 2.0 Food Packages - Clean Label Trends - PI Sugar Alcohols Qualified. Indicates if the product contains sugar alcohols. Extract based on known facts about the product even if not explicitly stated."}, "sugarAlcoholsStated": {"type": "string", "description": "NPI 2.0 Food Packages - Clean Label Trends - PI Sugar Alcohols Stated. Explicit statement about sugar alcohol content. Example: 'No Sugar Alcohols Added' or 'Sweetened with Sugar Alcohols'."}}}, "additionalInfo": {"type": "object", "description": "Additional product information and characteristics", "properties": {"energy": {"type": "number", "description": "Energy content in calories. Only extract if this is mentioned in the input data. Otherwise, set to null."}, "weight": {"type": "number", "description": "Total product weight in grams. Only extract if this is mentioned in the input data. Otherwise, set to null."}, "categories": {"type": "string", "description": "Product category classifications"}, "packaging": {"type": "string", "description": "Type of packaging material or container. For example, 'Plastic', 'Glass', 'Paper', 'Aluminum', 'Other'"}, "ecoscore": {"type": "string", "description": "Environmental impact score. Try to compute this from the ingredients and other product information. Follow the calculation method from Open Food Facts. Do not let this be null. Provide your best guess if not mentioned."}, "nova_group": {"type": "string", "description": "NOVA food classification group. Try to compute this from the ingredients and other product information. Follow the calculation method from Open Food Facts. Do not let this be null. Provide your best guess if not mentioned."}, "nutriscore_grade": {"type": "string", "description": "Nutri-Score grade (A to E) indicating nutritional quality. Try to compute this from the ingredients and other product information. Follow the calculation method from Open Food Facts. Do not let this be null. Provide your best guess if not mentioned."}, "data_source": {"type": "string", "description": "Always respond with 'Manufactruer_claims,FoodScanGenius_AI'"}, "dietary_preference": {"type": "object", "description": "Dietary preferences of the combined ingredients", "properties": {"Vegan": {"type": "boolean", "description": "Whether the combined ingredients are suitable for a vegan diet"}, "Vegetarian": {"type": "boolean", "description": "Whether the combined ingredients are suitable for a vegetarian diet"}, "Pescatarian": {"type": "boolean", "description": "Whether the combined ingredients are suitable for a pescatarian diet"}, "WhiteMeatOnly": {"type": "boolean", "description": "Whether the combined ingredients contain only white meat"}, "KetoFriendly": {"type": "boolean", "description": "Whether the combined ingredients are suitable for a ketogenic diet"}, "LowFodmap": {"type": "boolean", "description": "Whether the combined ingredients are suitable for a low FODMAP diet"}}}, "allergens": {"type": "object", "description": "Allergens present in the combined ingredients", "properties": {"Sugar": {"type": "boolean", "description": "Whether the combined ingredients contain sugar"}, "Celery": {"type": "boolean", "description": "Whether the combined ingredients contain celery"}, "Gluten": {"type": "boolean", "description": "Whether the combined ingredients contain gluten"}, "Crustaceans": {"type": "boolean", "description": "Whether the combined ingredients contain crustaceans"}, "Eggs": {"type": "boolean", "description": "Whether the combined ingredients contain eggs"}, "Fish": {"type": "boolean", "description": "Whether the combined ingredients contain fish"}, "Lupin": {"type": "boolean", "description": "Whether the combined ingredients contain lupin"}, "Milk": {"type": "boolean", "description": "Whether the combined ingredients contain milk"}, "Peanuts": {"type": "boolean", "description": "Whether the combined ingredients contain peanuts"}, "Sesame": {"type": "boolean", "description": "Whether the combined ingredients contain sesame"}, "TreeNuts": {"type": "boolean", "description": "Whether the combined ingredients contain tree nuts"}}}, "religious_labels": {"type": "object", "description": "Religious labels present in the combined ingredients", "properties": {"Halal": {"type": "boolean", "description": "Whether the combined ingredients are Halal certified"}, "Kosher": {"type": "boolean", "description": "Whether the combined ingredients are Kosher certified"}, "Hindu": {"type": "boolean", "description": "Whether the combined ingredients are suitable for Hindu dietary requirements"}, "Jain": {"type": "boolean", "description": "Whether the combined ingredients are suitable for Jain dietary requirements"}}}, "food_safety_labels": {"type": "object", "description": "Food safety labels present in the combined ingredients", "properties": {"GMO": {"type": "boolean", "description": "Whether the combined ingredients contain GMO components"}, "NoGMO": {"type": "boolean", "description": "Whether the combined ingredients are GMO-free"}, "Hormones": {"type": "boolean", "description": "Whether the combined ingredients contain hormones"}, "Carcinogenic": {"type": "boolean", "description": "Whether the combined ingredients are potentially carcinogenic"}, "Organic": {"type": "boolean", "description": "Whether the combined ingredients are organic"}, "ProductRecalls": {"type": "boolean", "description": "Whether the combined ingredients have been subject to product recalls"}}}, "sustainability_labels": {"type": "object", "description": "Sustainability labels present in the combined ingredients", "properties": {"Recycled": {"type": "boolean", "description": "Whether the combined ingredients are made from recycled materials"}, "AnimalWelfare": {"type": "boolean", "description": "Whether the combined ingredients meet animal welfare standards"}, "OrganicPositioning": {"type": "boolean", "description": "Whether the combined ingredients are positioned as organic"}, "PlantBased": {"type": "boolean", "description": "Whether the combined ingredients are plant-based"}, "SocialResponsibility": {"type": "boolean", "description": "Whether the combined ingredients meet social responsibility standards"}, "SustainablePackaging": {"type": "boolean", "description": "Whether the combined ingredients use sustainable packaging"}}}, "average_customer_rating": {"type": "number", "description": "Average customer rating of the product. Only extract if this is mentioned in the input data. Otherwise, set to null."}, "ASIN": {"type": "string", "description": "Amazon Standard Identification Number. Only extract if this is mentioned in the input data. Otherwise, set to null."}, "traces": {"type": "string", "description": "Potential trace ingredients or contamination warnings"}, "country_of_origin": {"type": "string", "description": "Country where the product was manufactured or produced. For example, 'United States', 'United Kingdom', 'France', 'Germany', 'Italy', 'Spain', 'Portugal', 'Greece', 'Turkey', 'Other'"}, "customerCareNumber": {"type": "string", "description": "Contact number for customer support"}, "email": {"type": "string", "description": "Contact email address for the manufacturer"}, "websiteLink": {"type": "string", "description": "Official product or manufacturer website URL"}}, "required": ["data_source", "dietary_preference", "allergens", "religious_labels", "food_safety_labels", "sustainability_labels", "country_of_origin", "customerCareNumber", "email", "websiteLink"]}}, "definitions": {"Ingredient": {"type": "object", "description": "Nested structure of ingredients used in the product", "additionalProperties": false, "properties": {"text": {"type": "string", "description": "Name of the ingredient. Do not add more than one ingredient in the text field. For example, text can be 'Basmati Rice' or 'Biryani Paste' or 'Whole Spices' or 'Mango' or 'Chocolate' etc. and not 'Basmati Rice, Biryani Paste, Whole Spices, Mango, Chocolate'."}, "amount": {"type": "number", "description": "Total amount of all ingredients combined. Guesstimate if not mentioned. Do not let this be null and provide your best guess."}, "amountUOM": {"type": "string", "description": "Unit of measurement for the total amount. Guesstimate if not mentioned. Do not let this be null and provide your best guess."}, "dietary_preference": {"type": "object", "description": "Dietary preferences of the combined ingredients", "properties": {"Vegan": {"type": "boolean", "description": "Whether the combined ingredients are suitable for a vegan diet"}, "Vegetarian": {"type": "boolean", "description": "Whether the combined ingredients are suitable for a vegetarian diet"}, "Pescatarian": {"type": "boolean", "description": "Whether the combined ingredients are suitable for a pescatarian diet"}, "WhiteMeatOnly": {"type": "boolean", "description": "Whether the combined ingredients contain only white meat"}, "KetoFriendly": {"type": "boolean", "description": "Whether the combined ingredients are suitable for a ketogenic diet"}, "LowFodmap": {"type": "boolean", "description": "Whether the combined ingredients are suitable for a low FODMAP diet"}}}, "allergens": {"type": "object", "description": "Allergens present in the combined ingredients", "properties": {"Sugar": {"type": "boolean", "description": "Whether the combined ingredients contain sugar"}, "Celery": {"type": "boolean", "description": "Whether the combined ingredients contain celery"}, "Gluten": {"type": "boolean", "description": "Whether the combined ingredients contain gluten"}, "Crustaceans": {"type": "boolean", "description": "Whether the combined ingredients contain crustaceans"}, "Eggs": {"type": "boolean", "description": "Whether the combined ingredients contain eggs"}, "Fish": {"type": "boolean", "description": "Whether the combined ingredients contain fish"}, "Lupin": {"type": "boolean", "description": "Whether the combined ingredients contain lupin"}, "Milk": {"type": "boolean", "description": "Whether the combined ingredients contain milk"}, "Peanuts": {"type": "boolean", "description": "Whether the combined ingredients contain peanuts"}, "Sesame": {"type": "boolean", "description": "Whether the combined ingredients contain sesame"}, "TreeNuts": {"type": "boolean", "description": "Whether the combined ingredients contain tree nuts"}}}, "religious_labels": {"type": "object", "description": "Religious labels present in the combined ingredients", "properties": {"Halal": {"type": "boolean", "description": "Whether the combined ingredients are Halal certified"}, "Kosher": {"type": "boolean", "description": "Whether the combined ingredients are Kosher certified"}, "Hindu": {"type": "boolean", "description": "Whether the combined ingredients are suitable for Hindu dietary requirements"}, "Jain": {"type": "boolean", "description": "Whether the combined ingredients are suitable for Jain dietary requirements"}}}, "food_safety_labels": {"type": "object", "description": "Food safety labels present in the combined ingredients", "properties": {"GMO": {"type": "boolean", "description": "Whether the combined ingredients contain GMO components"}, "NoGMO": {"type": "boolean", "description": "Whether the combined ingredients are GMO-free"}, "Hormones": {"type": "boolean", "description": "Whether the combined ingredients contain hormones"}, "Carcinogenic": {"type": "boolean", "description": "Whether the combined ingredients are potentially carcinogenic"}, "Organic": {"type": "boolean", "description": "Whether the combined ingredients are organic"}, "ProductRecalls": {"type": "boolean", "description": "Whether the combined ingredients have been subject to product recalls"}}}, "sustainability_labels": {"type": "object", "description": "Sustainability labels present in the combined ingredients", "properties": {"Recycled": {"type": "boolean", "description": "Whether the combined ingredients are made from recycled materials"}, "AnimalWelfare": {"type": "boolean", "description": "Whether the combined ingredients meet animal welfare standards"}, "OrganicPositioning": {"type": "boolean", "description": "Whether the combined ingredients are positioned as organic"}, "PlantBased": {"type": "boolean", "description": "Whether the combined ingredients are plant-based"}, "SocialResponsibility": {"type": "boolean", "description": "Whether the combined ingredients meet social responsibility standards"}, "SustainablePackaging": {"type": "boolean", "description": "Whether the combined ingredients use sustainable packaging"}}}, "subIngredients": {"type": "array", "description": "Any ingredients nested inside this one", "items": {"$ref": "#/definitions/Ingredient"}}, "source": {"type": "string", "enum": ["Amazon Images", "Product Scrape", "ScrapingQualificationAI"], "description": "Source of this ingredient information"}}, "required": ["text", "amount", "amountUOM", "dietary_preference", "allergens", "religious_labels", "food_safety_labels", "sustainability_labels", "source"]}}, "required": ["generalData", "servingSize", "nutritionalInformation", "ingredients", "claims", "NPI 2.0 Food Packages - Allergens & Intolerances", "cleanLabel", "additionalInfo"]}